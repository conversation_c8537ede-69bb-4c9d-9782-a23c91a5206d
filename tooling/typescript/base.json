{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"esModuleInterop": true, "skipLibCheck": true, "target": "ES2022", "lib": ["ES2022"], "allowJs": true, "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "incremental": true, "disableSourceOfProjectReferenceRedirect": true, "tsBuildInfoFile": "${configDir}/.cache/tsbuildinfo.json", "strict": true, "noUncheckedIndexedAccess": true, "checkJs": true, "module": "Preserve", "moduleResolution": "<PERSON><PERSON><PERSON>", "noEmit": true}, "exclude": ["node_modules", "build", "dist", ".next", ".expo"]}
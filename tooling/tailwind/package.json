{"name": "@cashanova/tailwind-config", "version": "0.1.0", "private": true, "type": "module", "exports": {"./native": "./native.ts", "./web": "./web.ts"}, "license": "MIT", "scripts": {"clean": "git clean -xdf .cache .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "dependencies": {"postcss": "^8.5.4", "tailwindcss": "catalog:", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@cashanova/eslint-config": "workspace:*", "@cashanova/prettier-config": "workspace:*", "@cashanova/tsconfig": "workspace:*", "@types/node": "^22.15.29", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@cashanova/prettier-config"}
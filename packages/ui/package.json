{"name": "@cashanova/ui", "private": true, "version": "0.1.0", "type": "module", "exports": {".": "./src/index.ts", "./theme": "./src/theme.tsx", "./toast": "./src/toast.tsx", "./button": "./src/button.tsx", "./dropdown-menu": "./src/dropdown-menu.tsx", "./card": "./src/card.tsx", "./chart": "./src/chart.tsx", "./table": "./src/table.tsx", "./data-table": "./src/data-table.tsx", "./checkbox": "./src/checkbox.tsx", "./progress": "./src/progress.tsx", "./skeleton": "./src/skeleton.tsx", "./sidebar": "./src/sidebar.tsx", "./separator": "./src/separator.tsx", "./sheet": "./src/sheet.tsx", "./input": "./src/input.tsx", "./tooltip": "./src/tooltip.tsx", "./use-mobile": "./src/hooks/use-mobile.ts", "./avatar": "./src/avatar.tsx", "./breadcrumb": "./src/breadcrumb.tsx", "./alert": "./src/alert.tsx", "./badge": "./src/badge.tsx", "./accordion": "./src/accordion.tsx", "./calendar": "./src/calendar.tsx", "./label": "./src/label.tsx", "./select": "./src/select.tsx"}, "license": "MIT", "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false", "ui-add": "pnpm dlx shadcn@latest add && pnpm format"}, "devDependencies": {"@cashanova/eslint-config": "workspace:*", "@cashanova/prettier-config": "workspace:*", "@cashanova/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@cashanova/prettier-config", "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "lucide-react": "catalog:", "next-themes": "^0.4.6", "react-day-picker": "^9.8.0", "recharts": "^3.0.2", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4"}}
import type { FunctionReference } from 'convex/server'
import { useEffect, useState } from 'react'
import { useConvexAuth, useQuery } from 'convex/react'

export function useDelayedQuery<Query extends FunctionReference<'query'>>(
	convexFuncRef: Query,
	args?: Query['_args']
) {
	const { isLoading, isAuthenticated } = useConvexAuth()
	const [shouldQuery, setShouldQuery] = useState(false)

	useEffect(() => {
		if (!isLoading && isAuthenticated) setShouldQuery(true)
	}, [isLoading, isAuthenticated])

	const result = useQuery(convexFuncRef, shouldQuery ? args : ('skip' as const))

	// eslint-disable-next-line @typescript-eslint/no-unsafe-return
	return result
}

import type { AccountsCategorized } from '@cashanova/types'
import { v } from 'convex/values'

import { internalQuery, query } from '@cashanova/backend/server'

export const getAccounts = query({
	args: {},
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) {
			throw new Error('Unauthorized')
		}

		const accounts = await ctx.db
			.query('accounts')
			.filter((q) => q.eq(q.field('userId'), identity.subject))
			.collect()

		return accounts
	},
})

export const _getAccounts = internalQuery({
	args: {
		userId: v.string(),
	},
	handler: async (ctx, args) => {
		const accounts = await ctx.db
			.query('accounts')
			.filter((q) => q.eq(q.field('userId'), args.userId))
			.collect()

		return accounts
	},
})

export const getAccountsWithCategories = query({
	args: {},
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) throw new Error('Unauthorized')

		const result: AccountsCategorized = {
			accounts: {
				checking: [],
				savings: [],
				investment: [],
				debt: [],
			},
			totals: {
				checking: 0,
				savings: 0,
				investment: 0,
				debt: 0,
			},
		}

		const accounts = await ctx.db
			.query('accounts')
			.filter((q) => q.eq(q.field('userId'), identity.subject))
			.collect()

		accounts.forEach((account) => {
			const amount = account.balance.current ?? 0
			const accountInfo = {
				name: account.name,
				amount,
			}

			switch (account.type.toLowerCase()) {
				case 'depository':
					switch (account.subType.toLowerCase()) {
						case 'checking':
							result.accounts.checking.push(accountInfo)
							result.totals.checking += amount
							break
						case 'savings':
							result.accounts.savings.push(accountInfo)
							result.totals.savings += amount
							break
						default:
							// Default to checking for unknown depository subtypes
							result.accounts.checking.push(accountInfo)
							result.totals.checking += amount
							break
					}
					break
				case 'credit':
				case 'loan':
					result.accounts.debt.push(accountInfo)
					result.totals.debt += amount
					break
				case 'investment':
					result.accounts.investment.push(accountInfo)
					result.totals.investment += amount
					break
				default:
					// For unknown types, try to classify based on subtype
					if (
						account.subType.toLowerCase().includes('credit') ||
						account.subType.toLowerCase().includes('loan')
					) {
						result.accounts.debt.push(accountInfo)
						result.totals.debt += amount
					} else if (account.subType.toLowerCase().includes('investment')) {
						result.accounts.investment.push(accountInfo)
					}
					break
			}
		})

		return result
	},
})

'use node'

import type { TransactionsSyncResponse } from 'plaid'
import { v } from 'convex/values'

import { internal } from '@cashanova/backend/api'
import { internalAction } from '@cashanova/backend/server'
import { plaid, tryCatch } from '@cashanova/utils'

export const _syncTransactions = internalAction({
	args: {
		userId: v.string(),
		daysRequested: v.optional(v.number()),
	},
	handler: async (ctx, args) => {
		// get cursors and accounts
		const [cursors, accounts] = await Promise.all([
			ctx.runQuery(internal.transactions.queries._getTransactionSyncCursors, {
				userId: args.userId,
			}),
			ctx.runQuery(internal.accounts.queries._getAccounts, {
				userId: args.userId,
			}),
		])

		// make sure there is only one cursor per account
		const cursorsByAccountId = cursors.reduce(
			(acc, cursor) => {
				cursor.cursors.forEach((c) => {
					acc[c.accountId] = c.cursor
				})
				return acc
			},
			{} as Record<string, string>
		)

		// sync transactions for each account
		const transactionsUpdated: TransactionsSyncResponse[] = []
		for (const account of accounts) {
			const { data: syncResponse, error } = await tryCatch(
				plaid.transactionsSync({
					access_token: account.plaidAccessToken,
					cursor: cursorsByAccountId[account.pliadAccountId] ?? undefined,
					options: {
						days_requested: args.daysRequested,
					},
				})
			)

			if (error) {
				console.error('Error syncing transactions', error)
				throw new Error(error.message)
			}

			transactionsUpdated.push(syncResponse.data)
		}

		// update database with transactions`
		await ctx.runMutation(internal.transactions.mutation._updateTransactions, {
			userId: args.userId,
			transactions: {
				added: transactionsUpdated.flatMap((transaction) =>
					transaction.added.flatMap((t) => ({
						userId: args.userId,
						accountId: t.account_id,
						transactionId: t.transaction_id,
						amount: t.amount,
						date: t.datetime ?? new Date().toISOString(),
						description: t.name,
						pending: t.pending,
						ignored: false,
						ISOCurrencyCode:
							t.iso_currency_code ?? t.unofficial_currency_code ?? 'USD',
						merchant: {
							name: t.merchant_name ?? 'unknown',
							logoUrl: t.logo_url ?? undefined,
						},
						category: {
							primary: t.personal_finance_category?.primary ?? 'UNKNOWN',
							detailed: t.personal_finance_category?.detailed ?? 'UNKNOWN',
							confidence:
								t.personal_finance_category?.confidence_level ??
								'UNKNOWN',
						},
					}))
				),
				modified: transactionsUpdated.flatMap((transaction) =>
					transaction.modified.flatMap((t) => ({
						transactionId: t.transaction_id,
						amount: t.amount,
						date: t.datetime ?? new Date().toISOString(),
					}))
				),
				deletedTransactionIds: transactionsUpdated.flatMap((transaction) =>
					transaction.removed.map((t) => t.transaction_id)
				),
			},
		})

		// Batch all cursor updates into a single call
		const allCursors = transactionsUpdated.flatMap((transaction) =>
			transaction.accounts.map((account) => ({
				accountId: account.account_id,
				cursor: transaction.next_cursor,
			}))
		)

		await ctx.runMutation(internal.transactions.mutation._updateCursors, {
			userId: args.userId,
			cursors: allCursors,
		})
	},
})

import { partial } from 'convex-helpers/validators'
import { v } from 'convex/values'

import { internalMutation, mutation } from '@cashanova/backend/server'
import { transactionsStructure } from '@cashanova/types/transactions'

import { internal } from '../_generated/api'

export const createTransactionRule = mutation({
	args: {
		transactionId: v.id('transactions'),
		action: v.object({
			changeCategory: v.optional(v.string()),
			renameTransaction: v.optional(v.string()),
			ignoreTransaction: v.optional(v.boolean()),
		}),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) throw new Error('Unauthorized')

		const transaction = await ctx.db.get(args.transactionId)
		if (!transaction) throw new Error('Transaction not found')

		await ctx.db.insert('rules', {
			userId: identity.subject,
			match: {
				name: {
					type: 'contains',
					value: transaction.description,
				},
			},
			action: args.action,
		})

		if (args.action.changeCategory) {
			await ctx.runMutation(
				internal.transactions.mutation._recategorizeTransaction,
				{
					userId: identity.subject,
					name: transaction.description,
					newCategory: args.action.changeCategory,
				}
			)
		}
	},
})

export const _recategorizeTransaction = internalMutation({
	args: {
		userId: v.string(),
		name: v.string(),
		newCategory: v.string(),
	},
	handler: async (ctx, args) => {
		const transactions = await ctx.db
			.query('transactions')
			.filter((q) => q.eq(q.field('userId'), args.userId))
			.filter((q) => q.eq(q.field('description'), args.name))
			.collect()

		for (const transaction of transactions) {
			await ctx.db.patch(transaction._id, {
				category: {
					primary: args.newCategory,
					detailed: args.newCategory,
					confidence: 'USER_DEFINED',
				},
			})
		}
	},
})

export const _updateTransactions = internalMutation({
	args: {
		userId: v.string(),
		transactions: v.object({
			added: v.array(v.object(transactionsStructure)),
			modified: v.array(v.object(partial(transactionsStructure))),
			deletedTransactionIds: v.array(v.string()),
		}),
	},
	handler: async (ctx, args) => {
		const { added, modified, deletedTransactionIds } = args.transactions

		if (added.length > 0) {
			for (const transaction of added) {
				await ctx.db.insert('transactions', transaction)
			}
		}

		if (modified.length > 0) {
			for (const transaction of modified) {
				const dbTransaction = await ctx.db
					.query('transactions')
					.filter((q) =>
						q.eq(q.field('transactionId'), transaction.transactionId)
					)
					.first()

				if (dbTransaction) {
					await ctx.db.patch(dbTransaction._id, {
						...transaction,
					})
				}
			}
		}

		if (deletedTransactionIds.length > 0) {
			for (const id of deletedTransactionIds) {
				const transaction = await ctx.db
					.query('transactions')
					.filter((q) => q.eq(q.field('transactionId'), id))
					.first()

				if (!transaction) continue

				await ctx.db.delete(transaction._id)
			}
		}
	},
})

export const _updateCursors = internalMutation({
	args: {
		userId: v.string(),
		cursors: v.array(
			v.object({
				cursor: v.string(),
				accountId: v.string(),
			})
		),
	},
	handler: async (ctx, args) => {
		const transactionSyncCursors = await ctx.db
			.query('transactionSyncCursors')
			.filter((q) => q.eq(q.field('userId'), args.userId))
			.first()

		if (!transactionSyncCursors) {
			await ctx.db.insert('transactionSyncCursors', {
				userId: args.userId,
				cursors: args.cursors,
			})

			return
		}

		// Merge new cursors with existing ones
		const existingCursors = new Map(
			transactionSyncCursors.cursors.map((c) => [c.accountId, c.cursor])
		)
		const newCursors = new Map(args.cursors.map((c) => [c.accountId, c.cursor]))

		// Merge maps (new cursors take precedence)
		const mergedCursors = [...existingCursors, ...newCursors].map(
			([accountId, cursor]) => ({
				accountId,
				cursor,
			})
		)

		await ctx.db.patch(transactionSyncCursors._id, {
			cursors: mergedCursors,
		})
	},
})

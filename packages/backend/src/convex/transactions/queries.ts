import type {
	CurrentMonthStats,
	IncomeSourceType,
	TransactionType,
} from '@cashanova/types/transactions'
import { paginationOptsValidator } from 'convex/server'
import { v } from 'convex/values'

import { internalQuery, query } from '@cashanova/backend/server'

export const getTransactions = query({
	args: {
		paginationOpts: paginationOptsValidator,
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) {
			throw new Error('Unauthorized')
		}

		const transactions = await ctx.db
			.query('transactions')
			.filter((q) => q.eq(q.field('userId'), identity.subject))
			.order('desc')
			.paginate(args.paginationOpts)

		return transactions
	},
})

export const _getAllTransactions = internalQuery({
	args: {
		userId: v.string(),
	},
	handler: async (ctx, args) => {
		const transactions = await ctx.db
			.query('transactions')
			.filter((q) => q.eq(q.field('userId'), args.userId))
			.collect()

		return transactions as TransactionType[]
	},
})

export const _getTransactionSyncCursors = internalQuery({
	args: {
		userId: v.string(),
	},
	handler: async (ctx, args) => {
		const cursors = await ctx.db
			.query('transactionSyncCursors')
			.filter((q) => q.eq(q.field('userId'), args.userId))
			.collect()

		return cursors
	},
})

export const getIncomeSources = query({
	args: {},
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) throw new Error('Unauthorized')

		const incomeTransactions = await ctx.db
			.query('transactions')
			.filter((q) => q.eq(q.field('userId'), identity.subject))
			.filter((q) => q.eq(q.field('category.primary'), 'INCOME'))
			.collect()

		// Group transactions by merchant name to identify income sources
		const incomeSourcesMap = new Map<string, TransactionType[]>()

		for (const transaction of incomeTransactions) {
			const merchantName = transaction.merchant.name
			if (!incomeSourcesMap.has(merchantName)) {
				incomeSourcesMap.set(merchantName, [])
			}
			const transactions = incomeSourcesMap.get(merchantName)
			if (transactions) {
				transactions.push(transaction)
			}
		}

		// Calculate frequency for each income source
		const incomeSources: IncomeSourceType[] = []

		for (const [merchantName, transactions] of incomeSourcesMap) {
			if (transactions.length < 2) continue // Need at least 2 transactions to determine frequency

			// Sort transactions by date
			const sortedTransactions = transactions.sort(
				(a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
			)

			// Calculate average amount
			const totalAmount = transactions.reduce((sum, t) => sum + t.amount, 0)
			const averageAmount = totalAmount / transactions.length

			// Calculate frequency based on date patterns
			const frequency = calculateIncomeFrequency(sortedTransactions)

			incomeSources.push({
				name: merchantName,
				amount: averageAmount,
				frequency,
			})
		}

		return incomeSources
	},
})

export const getTransactionsWithLimit = query({
	args: {
		limit: v.number(),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) throw new Error('Unauthorized')

		const transactions = await ctx.db
			.query('transactions')
			.filter((q) => q.eq(q.field('userId'), identity.subject))
			.order('desc')
			.take(args.limit)

		return transactions
	},
})

export const getCurrentMonthStats = query({
	args: {},
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) throw new Error('Unauthorized')

		const now = new Date()
		const twoMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 1, 1)

		const transactions = await ctx.db
			.query('transactions')
			.filter((q) => q.eq(q.field('userId'), identity.subject))
			.filter((q) => q.gte(q.field('date'), twoMonthsAgo.toISOString()))
			.collect()

		const lastMonthTransactions = transactions.filter((t) => {
			const transactionDate = new Date(t.date)
			return (
				transactionDate.getFullYear() === twoMonthsAgo.getFullYear() &&
				transactionDate.getMonth() === twoMonthsAgo.getMonth()
			)
		})

		const currentMonthTransactions = transactions.filter((t) => {
			const transactionDate = new Date(t.date)
			return (
				transactionDate.getFullYear() === now.getFullYear() &&
				transactionDate.getMonth() === now.getMonth()
			)
		})

		const lastMonthTotalSpent = lastMonthTransactions.reduce((sum, t) => {
			if (t.category.primary !== 'INCOME') {
				return sum + t.amount
			}

			return sum
		}, 0)

		const currentMonthTotalSpent = currentMonthTransactions.reduce((sum, t) => {
			if (t.category.primary !== 'INCOME') {
				return sum + t.amount
			}
			return sum
		}, 0)

		const percentChange =
			(currentMonthTotalSpent - lastMonthTotalSpent) / lastMonthTotalSpent

		const currentMonthIncome = currentMonthTransactions.reduce((sum, t) => {
			if (t.category.primary === 'INCOME') {
				return sum + t.amount
			}
			return sum
		}, 0)

		const currentMonthStats: CurrentMonthStats = {
			cashFlow: {
				actualIncome: currentMonthIncome,
				actualSpent: currentMonthTotalSpent,
			},
			budget: {
				totalSpent: currentMonthTotalSpent,
				percentChange,
				percentChangeDirection: percentChange > 0 ? 'up' : 'down',
				spentAmountChange: Math.abs(currentMonthTotalSpent - lastMonthTotalSpent),
				leftToSpend: 0,
			},
			payday: {
				daysUntil: 0,
				amount: 0,
			},
		}

		return currentMonthStats
	},
})

export const getDummySpendingData = query({
	args: {},
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) throw new Error('Unauthorized')

		// Get current date and calculate month ranges
		const now = new Date()
		const currentMonth = now.getMonth()
		const currentYear = now.getFullYear()

		// Get the number of days in each month
		const daysInCurrentMonth = new Date(currentYear, currentMonth + 1, 0).getDate()
		const daysInLastMonth = new Date(currentYear, currentMonth, 0).getDate()

		// Use the smaller number of days to align them
		const alignedDays = Math.min(daysInCurrentMonth, daysInLastMonth)

		// Generate realistic spending patterns
		const generateDailySpending = (baseAmount: number, variance: number) => {
			return Math.max(0, baseAmount + (Math.random() - 0.5) * variance)
		}

		// Create data arrays
		const currentMonthData: { date: string; amount: number }[] = []
		const lastMonthData: { date: string; amount: number }[] = []

		// Base spending patterns (higher on weekends, paydays, etc.)
		const baseDailySpending = 45 // Average daily spending
		const weekendMultiplier = 1.3 // 30% more on weekends
		const paydayMultiplier = 1.5 // 50% more on paydays (assuming 1st and 15th)

		for (let day = 1; day <= alignedDays; day++) {
			const currentDate = new Date(currentYear, currentMonth, day)
			const lastMonthDate = new Date(currentYear, currentMonth - 1, day)

			// Determine if it's a weekend
			const isWeekend = currentDate.getDay() === 0 || currentDate.getDay() === 6
			const isPayday = day === 1 || day === 15

			// Calculate spending multipliers
			let multiplier = 1
			if (isWeekend) multiplier *= weekendMultiplier
			if (isPayday) multiplier *= paydayMultiplier

			// Generate spending amounts
			const currentMonthSpending = generateDailySpending(
				baseDailySpending * multiplier,
				20
			)
			const lastMonthSpending = generateDailySpending(
				baseDailySpending * multiplier,
				20
			)

			currentMonthData.push({
				date: currentDate.toISOString().split('T')[0] ?? '',
				amount: Math.round(currentMonthSpending * 100) / 100,
			})

			lastMonthData.push({
				date: lastMonthDate.toISOString().split('T')[0] ?? '',
				amount: Math.round(lastMonthSpending * 100) / 100,
			})
		}

		// Combine both months into a single array with aligned data
		const combinedData: { date: string; currentMonth: number; lastMonth: number }[] =
			[]

		for (let i = 0; i < alignedDays; i++) {
			const currentDay = currentMonthData[i]
			const lastDay = lastMonthData[i]

			if (currentDay && lastDay) {
				combinedData.push({
					date: currentDay.date,
					currentMonth: currentDay.amount,
					lastMonth: lastDay.amount,
				})
			}
		}

		return combinedData
	},
})

export const searchTransactions = query({
	args: {
		name: v.optional(
			v.object({
				type: v.union(v.literal('contains'), v.literal('equals')),
				value: v.string(),
			})
		),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) throw new Error('Unauthorized')
		if (!args.name) return null

		const transactions = await ctx.db
			.query('transactions')
			.withSearchIndex('byTransactionDesc', (q) =>
				q.search('description', args.name?.value ?? '')
			)
			.filter((q) => q.eq(q.field('userId'), identity.subject))
			.take(10)

		return transactions
	},
})

// Helper function to determine income frequency based on transaction dates
function calculateIncomeFrequency(
	transactions: TransactionType[]
): 'weekly' | 'monthly' | 'semi-monthly' | 'bi-weekly' {
	if (transactions.length < 2) return 'monthly' // Default fallback

	const dates = transactions.map((t) => new Date(t.date))
	const intervals: number[] = []

	// Calculate intervals between consecutive transactions (in days)
	for (let i = 1; i < dates.length; i++) {
		const currentDate = dates[i]
		const previousDate = dates[i - 1]
		if (currentDate && previousDate) {
			const diffTime = currentDate.getTime() - previousDate.getTime()
			const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24))
			intervals.push(diffDays)
		}
	}

	// Calculate average interval
	const avgInterval =
		intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length

	// Determine frequency based on average interval
	if (avgInterval <= 7) {
		return 'weekly'
	} else if (avgInterval <= 14) {
		return 'bi-weekly'
	} else if (avgInterval <= 16) {
		return 'semi-monthly'
	} else {
		return 'monthly'
	}
}

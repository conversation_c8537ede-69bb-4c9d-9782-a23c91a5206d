import { v } from 'convex/values'

import { internalMutation } from '@cashanova/backend/server'
import { budgetStructure } from '@cashanova/types/budget'

export const _setBudgets = internalMutation({
	args: {
		budget: v.object(budgetStructure),
	},
	handler: async (ctx, args) => {
		const budget = await ctx.db
			.query('budgets')
			.filter((q) => q.eq(q.field('userId'), args.budget.userId))
			.first()

		if (!budget) {
			await ctx.db.insert('budgets', {
				...args.budget,
			})
		} else {
			await ctx.db.patch(budget._id, {
				categories: args.budget.categories,
			})
		}
	},
})

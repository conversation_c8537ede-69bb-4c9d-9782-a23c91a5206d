import { createClerkClient } from '@clerk/backend'
import { partial } from 'convex-helpers/validators'
import { v } from 'convex/values'

import { internalMutation, mutation } from '@cashanova/backend/server'
import { onboardingSchema } from '@cashanova/types'
import { accountsStructure } from '@cashanova/types/transactions'

export const startOnboarding = mutation({
	args: {},
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) throw new Error('Unauthorized')

		await ctx.db.insert('onboarding', {
			userId: identity.subject,
			currentStep: 0,
			plaidLinkConnected: false,
			accounts: [],
			incomeSources: [],
			confirmedIncomeSources: [],
		})
	},
})

export const updateOnboarding = mutation({
	args: {
		onboardingId: v.id('onboarding'),
		opts: partial(onboardingSchema),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) throw new Error('Unauthorized')

		await ctx.db.patch(args.onboardingId, args.opts)
	},
})

export const _finishOnboarding = internalMutation({
	args: {
		onboardingId: v.id('onboarding'),
		userId: v.string(),
	},
	handler: async (ctx, args) => {
		const clerk = createClerkClient({
			secretKey: process.env.CLERK_SECRET_KEY,
		})

		await clerk.users.updateUserMetadata(args.userId, {
			privateMetadata: {
				onboarded: true,
			},
		})

		await ctx.db.delete(args.onboardingId)
	},
})

export const _saveAccount = internalMutation({
	args: {
		accessToken: v.string(),
		publicToken: v.string(),
		userId: v.string(),
		accounts: v.array(v.object(accountsStructure)),
	},
	handler: async (ctx, args) => {
		for (const account of args.accounts) {
			await ctx.db.insert('accounts', {
				userId: args.userId,
				pliadAccountId: account.pliadAccountId,
				plaidAccessToken: args.accessToken,
				plaidPublicToken: args.publicToken,
				holderCategory: account.holderCategory,
				mask: account.mask,
				name: account.name,
				officialName: account.officialName,
				subType: account.subType,
				type: account.type,
				balance: {
					available: account.balance.available,
					current: account.balance.current,
					isoCurrencyCode: account.balance.isoCurrencyCode,
					limit: account.balance.limit,
				},
			})
		}
	},
})

'use node'

import { createClerkClient } from '@clerk/backend'
import { v } from 'convex/values'

import { internal } from '@cashanova/backend/api'
import { action } from '@cashanova/backend/server'
import { plaid, tryCatch } from '@cashanova/utils'

export const handleAccountLink = action({
	args: {
		publicToken: v.string(),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) {
			throw new Error('Unauthorized')
		}

		const { accessToken } = await ctx.runAction(
			internal.plaid.actions._exchangePublicToken,
			{
				publicToken: args.publicToken,
			}
		)

		const { data, error } = await tryCatch(
			plaid.accountsGet({
				access_token: accessToken,
			})
		)

		if (error) {
			console.error('Error getting accounts', error)
			throw new Error(error.message)
		}

		await ctx.runMutation(internal.onboarding.mutations._saveAccount, {
			userId: identity.subject,
			accessToken,
			publicToken: args.publicToken,
			accounts: data.data.accounts.map((account) => ({
				userId: identity.subject,
				plaidAccessToken: accessToken,
				plaidPublicToken: args.publicToken,
				pliadAccountId: account.account_id,
				holderCategory: account.holder_category ?? 'unknown',
				mask: account.mask ?? '',
				name: account.name,
				officialName: account.official_name ?? '',
				subType: account.subtype ?? 'unknown',
				type: account.type,
				balance: {
					available: account.balances.available ?? undefined,
					current: account.balances.current ?? undefined,
					isoCurrencyCode: account.balances.iso_currency_code ?? undefined,
					limit: account.balances.limit ?? undefined,
				},
			})),
		})
	},
})

export const fetchInitialTransactions = action({
	args: {},
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) throw new Error('Unauthorized')

		await ctx.runAction(internal.transactions.actions._syncTransactions, {
			userId: identity.subject,
			daysRequested: 90,
		})
	},
})

export const markAsOnboarded = action({
	args: {},
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) throw new Error('Unauthorized')

		const clerkClient = createClerkClient({
			secretKey: process.env.CLERK_SECRET_KEY,
		})

		await clerkClient.users.updateUserMetadata(identity.subject, {
			privateMetadata: {
				onboarded: true,
			},
		})
	},
})

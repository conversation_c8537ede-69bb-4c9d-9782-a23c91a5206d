import { query } from '@cashanova/backend/server'

export const getOnboarding = query({
	args: {},
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) throw new Error('Unauthorized')

		const onboarding = await ctx.db
			.query('onboarding')
			.withIndex('byUserId')
			.filter((q) => q.eq(q.field('userId'), identity.subject))
			.first()

		return onboarding
	},
})

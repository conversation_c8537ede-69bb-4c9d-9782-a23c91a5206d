'use node'

import { v } from 'convex/values'
import { CountryCode, Products } from 'plaid'

import { action, internalAction } from '@cashanova/backend/server'
import { plaid, tryCatch } from '@cashanova/utils'

export const createLinkToken = action({
	args: {},
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) {
			throw new Error('Unauthorized')
		}

		const { data, error } = await tryCatch(
			plaid.linkTokenCreate({
				client_id: process.env.PLAID_CLIENT_ID,
				secret: process.env.PLAID_SECRET,
				user: {
					client_user_id: identity.subject,
				},
				client_name: 'Cashanova',
				products: [Products.Transactions],
				country_codes: [CountryCode.Us],
				language: 'en',
			})
		)

		if (error) {
			console.error('Error creating link token', error)
			throw new Error(error.message)
		}

		return data.data.link_token
	},
})

export const _exchangePublicToken = internalAction({
	args: {
		publicToken: v.string(),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) {
			throw new Error('Unauthorized')
		}

		const { data, error } = await tryCatch(
			plaid.itemPublicTokenExchange({
				public_token: args.publicToken,
			})
		)

		if (error) {
			console.error('Error exchanging public token', error)
			throw new Error(error.message)
		}

		return {
			accessToken: data.data.access_token,
			itemId: data.data.item_id,
		}
	},
})

import { defineSchema, defineTable } from 'convex/server'
import { v } from 'convex/values'

import { budgetStructure } from '@cashanova/types/budget'
import { onboardingSchema } from '@cashanova/types/onboarding'
import {
	accountsStructure,
	threadTypes,
	transactionsStructure,
} from '@cashanova/types/transactions'

const schema = defineSchema({
	accounts: defineTable(accountsStructure),
	transactions: defineTable(transactionsStructure).searchIndex('byTransactionDesc', {
		searchField: 'description',
		filterFields: ['userId', 'date', 'accountId', 'amount'],
	}),
	transactionSyncCursors: defineTable({
		userId: v.string(),
		cursors: v.array(
			v.object({
				accountId: v.string(),
				cursor: v.string(),
			})
		),
	}),
	onboarding: defineTable(onboardingSchema).index('byUserId', ['userId']),
	threads: defineTable({
		userId: v.string(),
		threadId: v.string(),
		type: threadTypes,
	}),
	budgets: defineTable(budgetStructure),
	incomeSources: defineTable({
		userId: v.string(),
		name: v.string(),
		amount: v.number(),
		frequency: v.union(
			v.literal('weekly'),
			v.literal('monthly'),
			v.literal('semi-monthly'),
			v.literal('bi-weekly')
		),
		nextPayday: v.optional(v.string()),
	}),
	rules: defineTable({
		userId: v.string(),
		match: v.object({
			name: v.optional(
				v.object({
					type: v.union(v.literal('contains'), v.literal('equals')),
					value: v.string(),
				})
			),
			amount: v.optional(
				v.object({
					accountType: v.union(
						v.literal('any'),
						v.literal('credit'),
						v.literal('debit')
					),
					boolean: v.union(
						v.literal('greaterThan'),
						v.literal('lessThan'),
						v.literal('equals'),
						v.literal('between')
					),
					amount: v.number(),
					amount2: v.optional(v.number()),
				})
			),
			account: v.optional(
				v.object({
					id: v.string(),
					name: v.string(),
				})
			),
			date: v.optional(
				v.object({
					after: v.string(),
				})
			),
		}),
		action: v.object({
			changeCategory: v.optional(v.string()),
			renameTransaction: v.optional(v.string()),
			ignoreTransaction: v.optional(v.boolean()),
		}),
	}),
})

export default schema

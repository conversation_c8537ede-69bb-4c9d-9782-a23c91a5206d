import { v } from 'convex/values'

import { query } from '@cashanova/backend/server'
import { threadTypes } from '@cashanova/types/transactions'

export const getThread = query({
	args: {
		threadId: v.optional(v.string()),
		type: threadTypes,
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) throw new Error('Unauthorized')

		const thread = await ctx.db
			.query('threads')
			.filter((q) => q.eq(q.field('userId'), identity.subject))
			.filter((q) => q.eq(q.field('type'), args.type))
			.filter((q) => q.eq(q.field('threadId'), args.threadId))
			.first()

		return thread ?? null
	},
})

import { v } from 'convex/values'
import { z } from 'zod/v3'

import { systemPrompt } from '@cashanova/ai'
import { internal } from '@cashanova/backend/api'
import { action, internalMutation } from '@cashanova/backend/server'
import { financeCategories, threadTypes } from '@cashanova/types/transactions'

import { cashanovaAgent } from './agent'

export const _createThread = internalMutation({
	args: {
		userId: v.string(),
		type: threadTypes,
	},
	handler: async (ctx, args) => {
		const { threadId } = await cashanovaAgent.createThread(ctx, {
			userId: args.userId,
		})

		await ctx.db.insert('threads', {
			userId: args.userId,
			threadId,
			type: args.type,
		})

		return { threadId }
	},
})

export const analyzeTransactions = action({
	args: {
		threadId: v.optional(v.string()),
		saveBudget: v.optional(v.boolean()),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity()
		if (!identity) throw new Error('Unauthorized')

		let threadId = args.threadId
		if (!threadId) {
			const threadData = (await ctx.runMutation(internal.ai.thread._createThread, {
				userId: identity.subject,
				type: 'transactions',
			})) as { threadId: string }

			threadId = threadData.threadId
		}

		const { thread } = await cashanovaAgent.continueThread(ctx, {
			threadId,
			userId: identity.subject,
		})

		const result = await thread.generateObject({
			prompt: `Please analyze the transactions and sort them into categories that best fits the 50/30/20 rule. Use the primary category as the label for the budget category and then determine where it falls under the 50/30/20 rule. 
			
			The categories should be a list of objects with the following properties: name, percentage, usagePercentage, amountSpent, amountAllocated. Sort the income sources as well into their own object with the following properties: name, amount, frequency. 
			
			In general, you should be able to determine the income sources from the transactions primary category. 
			
			Note: If this value is set "${args.saveBudget}" then you should also save the budget to the database using the setBudgets tool.`,
			system: systemPrompt,
			schema: z.object({
				categories: z.object({
					needs: z
						.object({
							name: z.literal('NEEDS').describe('The name of the category'),
							budgetPercentage: z
								.number()
								.describe(
									'The percentage of the budget that is allocated to the needs category. This should be a number between 0 and 100.'
								),
							usagePercentage: z
								.number()
								.describe(
									'What percentage of the needs category has been spent'
								),
							amountSpent: z
								.number()
								.describe('The total amount spent in the needs category'),
							amountAllocated: z
								.number()
								.describe(
									'The total amount allocated to the needs category.'
								),
							subCategories: z
								.array(
									z.object({
										name: z
											.string()
											.describe(
												`The name of the sub category. Once again these are more detailed categories such as: ${JSON.stringify(financeCategories).split(',').join(', ')}`
											),
										budgetPercentage: z
											.number()
											.describe(
												'The percentage of the budget that is allocated to the sub category'
											),
										usagePercentage: z
											.number()
											.describe(
												'The usage percentage of the sub category'
											),
										amountSpent: z
											.number()
											.describe(
												'The total amount spent in the sub category'
											),
										amountAllocated: z
											.number()
											.describe(
												'The total amount allocated to the sub category'
											),
									})
								)
								.describe(
									`These are more detailed categories such as: ${JSON.stringify(financeCategories).split(',').join(', ')}`
								),
						})
						.describe(
							"This is the needs category. Aim for 50% at minimum. It's okay to be more then 50% but SHOULD NOT EXCEED 55%"
						),
					wants: z
						.object({
							name: z.literal('WANTS').describe('The name of the category'),
							budgetPercentage: z
								.number()
								.describe(
									'The percentage of the budget that is allocated to the wants category. This should be a number between 0 and 100.'
								),
							usagePercentage: z
								.number()
								.describe(
									'What percentage of the needs category has been spent'
								),
							amountSpent: z
								.number()
								.describe('The total amount spent in the needs category'),
							amountAllocated: z
								.number()
								.describe(
									'The total amount allocated to the needs category.'
								),
							subCategories: z
								.array(
									z.object({
										name: z
											.string()
											.describe(
												`The name of the sub category. Once again these are more detailed categories such as: ${JSON.stringify(financeCategories).split(',').join(', ')}`
											),
										budgetPercentage: z
											.number()
											.describe(
												'The percentage of the budget that is allocated to the sub category'
											),
										usagePercentage: z
											.number()
											.describe(
												'The usage percentage of the sub category'
											),
										amountSpent: z
											.number()
											.describe(
												'The total amount spent in the sub category'
											),
										amountAllocated: z
											.number()
											.describe(
												'The total amount allocated to the sub category'
											),
									})
								)
								.describe(
									`These are more detailed categories such as: ${JSON.stringify(financeCategories).split(',').join(', ')}`
								),
						})
						.describe(
							"This is the wants category. Aim for 30% at maximum. It's okay to be less then or a little more then 30% but SHOULD NOT EXCEED 35%"
						),
					savings: z
						.object({
							name: z
								.literal('SAVINGS')
								.describe('The name of the category'),
							budgetPercentage: z
								.number()
								.describe(
									'The percentage of the budget that is allocated to the savings category. This should be a number between 0 and 100.'
								),
							usagePercentage: z
								.number()
								.describe(
									'What percentage of the needs category has been spent'
								),
							amountSpent: z
								.number()
								.describe('The total amount spent in the needs category'),
							amountAllocated: z
								.number()
								.describe(
									'The total amount allocated to the needs category.'
								),
							subCategories: z
								.array(
									z.object({
										name: z
											.string()
											.describe(
												`The name of the sub category. Once again these are more detailed categories such as: ${JSON.stringify(financeCategories).split(',').join(', ')}`
											),
										budgetPercentage: z
											.number()
											.describe(
												'The percentage of the budget that is allocated to the sub category'
											),
										usagePercentage: z
											.number()
											.describe(
												'The usage percentage of the sub category'
											),
										amountSpent: z
											.number()
											.describe(
												'The total amount spent in the sub category'
											),
										amountAllocated: z
											.number()
											.describe(
												'The total amount allocated to the sub category'
											),
									})
								)
								.describe(
									`These are more detailed categories such as: ${JSON.stringify(financeCategories).split(',').join(', ')}`
								),
						})
						.describe(
							'This is the savings category. This should be 20% of the total budget. If more is savings are viable then please make this percentage bigger'
						),
				}),
				budgetAmount: z.number().describe('The total amount for the budget'),
			}),
		})

		if (args.saveBudget) {
			await ctx.runMutation(internal.budgets.mutation._setBudgets, {
				budget: {
					userId: identity.subject,
					budgetAmount: result.object.budgetAmount,
					categories: result.object.categories,
				},
			})
		}

		return result.object
	},
})

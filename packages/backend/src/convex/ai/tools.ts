import type { TransactionType } from '@cashanova/types/transactions'
import { createTool } from '@convex-dev/agent'
import { z } from 'zod/v3'

import { internal } from '@cashanova/backend/api'

export const getTransactionsTool = createTool({
	description:
		'Get all transactions for the current user from the database. This contains all forms of transactions for example: income, spending, transfers, ect. This is the most important tool to use when sorting transactions',
	args: z.object({}),
	handler: async (ctx): Promise<TransactionType[]> => {
		if (!ctx.userId) throw new Error('Unauthorized')

		const transactions = await ctx.runQuery(
			internal.transactions.queries._getAllTransactions,
			{
				userId: ctx.userId,
			}
		)

		return transactions
	},
})

export const setBudgetsTool = createTool({
	description: 'Update the budgets for the current user',
	args: z.object({
		budgetAmount: z.number(),
		categories: z.object({
			needs: z.object({
				name: z.string(),
				budgetPercentage: z.number(),
				amountSpent: z.number(),
				amountAllocated: z.number(),
				usagePercentage: z.number(),
				subCategories: z.array(
					z.object({
						name: z.string(),
						budgetPercentage: z.number(),
						amountSpent: z.number(),
						amountAllocated: z.number(),
						usagePercentage: z.number(),
					})
				),
			}),
			wants: z.object({
				name: z.string(),
				budgetPercentage: z.number(),
				amountSpent: z.number(),
				amountAllocated: z.number(),
				usagePercentage: z.number(),
				subCategories: z.array(
					z.object({
						name: z.string(),
						budgetPercentage: z.number(),
						amountSpent: z.number(),
						amountAllocated: z.number(),
						usagePercentage: z.number(),
					})
				),
			}),
			savings: z.object({
				name: z.string(),
				budgetPercentage: z.number(),
				amountSpent: z.number(),
				amountAllocated: z.number(),
				usagePercentage: z.number(),
				subCategories: z.array(
					z.object({
						name: z.string(),
						budgetPercentage: z.number(),
						amountSpent: z.number(),
						amountAllocated: z.number(),
						usagePercentage: z.number(),
					})
				),
			}),
		}),
	}),
	handler: async (ctx, args) => {
		if (!ctx.userId) throw new Error('Unauthorized')

		await ctx.runMutation(internal.budgets.mutation._setBudgets, {
			budget: {
				userId: ctx.userId,
				budgetAmount: args.budgetAmount,
				categories: args.categories,
			},
		})
	},
})

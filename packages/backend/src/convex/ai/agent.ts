import { Agent } from '@convex-dev/agent'

import { model, systemPrompt } from '@cashanova/ai'
import { components } from '@cashanova/backend/api'

import { getTransactionsTool, setBudgetsTool } from './tools'

export const cashanovaAgent = new Agent(components.agent, {
	chat: model,
	instructions: systemPrompt,
	name: '<PERSON><PERSON><PERSON>',
	maxRetries: 5,
	maxSteps: 10,
	tools: {
		getTransactions: getTransactionsTool,
		setBudgets: setBudgetsTool,
	},
})

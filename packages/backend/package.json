{"name": "@cashanova/backend", "private": true, "version": "0.1.0", "type": "module", "exports": {"./nextjs": "./src/nextjs.ts", "./react": "./src/react.ts", "./schema": "./src/convex/schema.ts", "./api": {"types": "./src/convex/_generated/api.d.ts", "default": "./src/convex/_generated/api.js"}, "./dataModel": "./src/convex/_generated/dataModel.d.ts", "./server": {"types": "./src/convex/_generated/server.d.ts", "default": "./src/convex/_generated/server.js"}, "./hooks": {"default": "./src/hooks.ts"}}, "license": "MIT", "scripts": {"build": "pnpm convex deploy", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "pnpm convex dev", "convex:dev": "pnpm convex dev", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "devDependencies": {"@cashanova/eslint-config": "workspace:*", "@cashanova/prettier-config": "workspace:*", "@cashanova/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@cashanova/prettier-config", "dependencies": {"@cashanova/ai": "workspace:*", "@cashanova/types": "workspace:*", "@cashanova/utils": "workspace:*", "@clerk/backend": "^2.4.1", "@convex-dev/agent": "^0.1.14", "@convex-dev/workflow": "^0.2.5", "@t3-oss/env-core": "^0.13.8", "convex": "catalog:", "convex-helpers": "^0.1.99", "plaid": "catalog:", "zod": "catalog:"}}
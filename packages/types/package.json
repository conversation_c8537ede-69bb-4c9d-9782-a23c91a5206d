{"name": "@cashanova/types", "private": true, "version": "0.1.0", "type": "module", "exports": {".": "./src/index.ts", "./transactions": "./src/transactions.ts", "./onboarding": "./src/onboarding.ts", "./budget": "./src/budget.ts", "./rules": "./src/rules.ts"}, "license": "MIT", "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "devDependencies": {"@cashanova/eslint-config": "workspace:*", "@cashanova/prettier-config": "workspace:*", "@cashanova/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@cashanova/prettier-config", "dependencies": {"@tanstack/react-form": "catalog:", "convex": "catalog:", "plaid": "catalog:"}}
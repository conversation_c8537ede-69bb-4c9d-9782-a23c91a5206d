import type { Infer } from 'convex/values'
import { v } from 'convex/values'

const _categoryStructure = {
	name: v.string(),
	budgetPercentage: v.number(),
	usagePercentage: v.number(),
	amountSpent: v.number(),
	amountAllocated: v.number(),
}

export const budgetStructure = {
	userId: v.string(),
	budgetAmount: v.number(),
	categories: v.object({
		needs: v.object({
			..._categoryStructure,
			subCategories: v.array(v.object(_categoryStructure)),
		}),
		wants: v.object({
			..._categoryStructure,
			subCategories: v.array(v.object(_categoryStructure)),
		}),
		savings: v.object({
			..._categoryStructure,
			subCategories: v.array(v.object(_categoryStructure)),
		}),
	}),
}

const _budgetObject = v.object(budgetStructure)
export type BudgetType = Infer<typeof _budgetObject>

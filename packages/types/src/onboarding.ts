import type { Infer } from 'convex/values'
import { v } from 'convex/values'

export const onboardingSchema = v.object({
	userId: v.string(),
	currentStep: v.number(),
	plaidToken: v.optional(v.string()),
	plaidLinkConnected: v.boolean(),
	accounts: v.array(v.id('accounts')),
	incomeSources: v.array(v.id('incomeSources')),
	confirmedIncomeSources: v.array(v.id('incomeSources')),
	threadId: v.optional(v.id('threads')),
})

export type UpdateOnboardingSchema = Partial<Infer<typeof onboardingSchema>>

export interface OnboardingContextType {
	onboardingId: string | null
	state: Infer<typeof onboardingSchema> | null
	updateState: (opts: UpdateOnboardingSchema) => void
	nextStep: () => void
	prevStep: () => void
	goToStep: (step: number) => void
}

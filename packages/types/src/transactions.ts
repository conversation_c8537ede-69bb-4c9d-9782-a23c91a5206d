import type { Infer } from 'convex/values'
import { v } from 'convex/values'

export const threadTypes = v.union(v.literal('transactions'))

export const transactionsStructure = {
	userId: v.string(),
	accountId: v.string(),
	transactionId: v.string(),
	amount: v.number(),
	date: v.string(),
	description: v.string(),
	pending: v.boolean(),
	ignored: v.boolean(),
	ISOCurrencyCode: v.string(),
	merchant: v.object({
		name: v.string(),
		logoUrl: v.optional(v.string()),
	}),
	category: v.object({
		primary: v.string(),
		detailed: v.string(),
		confidence: v.string(),
	}),
}

const _transactionsObject = v.object(transactionsStructure)
export type TransactionType = Infer<typeof _transactionsObject>

export const accountsStructure = {
	userId: v.string(),
	pliadAccountId: v.string(),
	plaidAccessToken: v.string(),
	plaidPublicToken: v.string(),
	holderCategory: v.string(),
	mask: v.string(),
	name: v.string(),
	officialName: v.string(),
	subType: v.string(),
	type: v.string(),
	balance: v.object({
		available: v.optional(v.number()),
		current: v.optional(v.number()),
		isoCurrencyCode: v.optional(v.string()),
		limit: v.optional(v.number()),
	}),
}

const _accountsObject = v.object(accountsStructure)
export type AccountType = Infer<typeof _accountsObject>

export const incomeSourcesStructure = {
	name: v.string(),
	amount: v.number(),
	frequency: v.union(
		v.literal('weekly'),
		v.literal('monthly'),
		v.literal('semi-monthly'),
		v.literal('bi-weekly')
	),
}

const _incomeSourceObject = v.object(incomeSourcesStructure)
export type IncomeSourceType = Infer<typeof _incomeSourceObject>

export interface CurrentMonthStats {
	cashFlow: {
		actualIncome: number
		actualSpent: number
	}
	budget: {
		totalSpent: number
		percentChange: number
		percentChangeDirection: 'up' | 'down'
		spentAmountChange: number
		leftToSpend: number
	}
	payday: {
		daysUntil: number
		amount: number
	}
}

export const financeCategories = [
	'INCOME',
	'TRANSFER_IN',
	'TRANSFER_OUT',
	'LOAN_PAYMENTS',
	'BANK_FEES',
	'ENTERTAINMENT',
	'FOOD_AND_DRINK',
	'GENERAL_MERCHANDISE',
	'HOME_IMPROVEMENT',
	'MEDICAL',
	'PERSONAL_CARE',
	'GENERAL_SERVICES',
	'GOVERNMENT_AND_NON_PROFIT',
	'TRANSPORTATION',
	'TRAVEL',
	'RENT_AND_UTILITIES',
] as const
export type FinanceCategory = (typeof financeCategories)[number]

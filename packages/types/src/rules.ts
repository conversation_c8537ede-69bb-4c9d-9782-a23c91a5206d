import type { Dispatch, SetStateAction } from 'react'

export interface RuleState {
	match: {
		name?: {
			type: 'contains' | 'equals'
			value: string
		}
		amount?: {
			accountType: 'any' | 'credit' | 'debit'
			boolean: 'greaterThan' | 'lessThan' | 'equals' | 'between'
			amount: number
			amount2: number | undefined
		}
		account?: {
			id: string
			name: string
		}
		date?: {
			after: string
		}
	}
	action: {
		changeCategory?: string
		renameTransaction?: string
		ignoreTransaction?: boolean
	}
}

export interface RuleContextType {
	state: RuleState
	updateState: (state: Partial<RuleState>) => void

	selectedTransactionId: string | null
	setSelectedTransactionId: Dispatch<SetStateAction<string | null>>
}

import { Configuration, PlaidApi, PlaidEnvironments } from 'plaid'

export const plaidConfig = new Configuration({
	basePath:
		process.env.PLAID_ENV === 'prod'
			? PlaidEnvironments.production
			: PlaidEnvironments.sandbox,
	baseOptions: {
		headers: {
			'PLAID-CLIENT-ID': process.env.PLAID_CLIENT_ID,
			'PLAID-SECRET': process.env.PLAID_SECRET,
		},
	},
})

export const plaid = new PlaidApi(plaidConfig)

export const PlaidProducts = ['accounts', 'transactions'] as const
export const PlaidCountryCodes = ['US'] as const
export const PlaidLanguage = 'en'

{"name": "@cashanova/utils", "private": true, "version": "0.1.0", "type": "module", "exports": {".": "./src/index.ts", "./try-catch": "./src/lib/try-catch.ts", "./plaid": "./src/lib/plaid.ts"}, "license": "MIT", "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "devDependencies": {"@cashanova/eslint-config": "workspace:*", "@cashanova/prettier-config": "workspace:*", "@cashanova/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@cashanova/prettier-config", "dependencies": {"plaid": "catalog:", "react": "catalog:react19", "react-dom": "catalog:react19"}}
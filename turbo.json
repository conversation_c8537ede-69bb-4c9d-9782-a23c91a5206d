{"$schema": "https://turborepo.org/schema.json", "ui": "tui", "tasks": {"topo": {"dependsOn": ["^topo"]}, "build": {"dependsOn": ["^build"], "outputs": [".cache/tsbuildinfo.json", "dist/**"]}, "convex:build": {"cache": false, "interactive": true}, "dev": {"dependsOn": ["^dev"], "cache": false, "persistent": false}, "format": {"outputs": [".cache/.prettiercache"], "outputLogs": "new-only"}, "lint": {"dependsOn": ["^topo", "^build"], "outputs": [".cache/.eslintcache"]}, "typecheck": {"dependsOn": ["^topo", "^build"], "outputs": [".cache/tsbuildinfo.json"]}, "clean": {"cache": false}, "//#clean": {"cache": false}, "push": {"cache": false, "interactive": true}, "studio": {"cache": false, "persistent": true}, "ui-add": {"cache": false, "interactive": true}}, "globalEnv": ["PORT", "CLERK_FRONTEND_API_URL", "NEXT_PUBLIC_CONVEX_URL", "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY", "CLERK_SECRET_KEY", "PLAID_CLIENT_ID", "PLAID_SECRET", "PLAID_ENV", "GROQ_API_KEY", "CONVEX_DEPLOY_KEY"], "globalPassThroughEnv": ["NODE_ENV", "CI", "VERCEL", "VERCEL_ENV", "VERCEL_URL", "npm_lifecycle_event"]}
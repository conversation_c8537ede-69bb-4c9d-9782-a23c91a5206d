/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */

import './src/env'

import type { NextConfig } from 'next'

const config = {
	transpilePackages: ['@cashanova/ai', '@cashanova/backend', '@cashanova/ui'],

	eslint: { ignoreDuringBuilds: true },
	typescript: { ignoreBuildErrors: true },
} satisfies NextConfig

export default config

{"name": "@cashanova/nextjs", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "pnpm with-env next build", "clean": "git clean -xdf .cache .next .turbo node_modules", "dev": "pnpm with-env next dev --turbopack", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "start": "pnpm with-env next start", "typecheck": "tsc --noEmit", "with-env": "dotenv -e ../../.env --"}, "dependencies": {"@cashanova/backend": "workspace:*", "@cashanova/ui": "workspace:*", "@cashanova/utils": "workspace:*", "@clerk/nextjs": "^6.23.1", "@clerk/themes": "^2.3.3", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-form": "catalog:", "convex": "^1.25.4", "flags": "catalog:", "lucide-react": "catalog:", "next": "^15.4.4", "plaid": "catalog:", "react": "catalog:react19", "react-dom": "catalog:react19", "react-plaid-link": "^4.0.1", "tw-animate-css": "^1.3.4", "zod": "catalog:"}, "devDependencies": {"@cashanova/eslint-config": "workspace:*", "@cashanova/prettier-config": "workspace:*", "@cashanova/tailwind-config": "workspace:*", "@cashanova/tsconfig": "workspace:*", "@cashanova/types": "workspace:*", "@tailwindcss/postcss": "^4.1.11", "@types/minimatch": "^6.0.0", "@types/node": "^22.15.29", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "dotenv-cli": "^8.0.0", "eslint": "catalog:", "prettier": "catalog:", "tailwindcss": "catalog:", "types": "^0.1.1", "typescript": "catalog:"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "pnpm@10.11.1", "prettier": "@cashanova/prettier-config"}
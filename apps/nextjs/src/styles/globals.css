@import 'tailwindcss' source('../../src/');
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@source '../../../../packages/ui/src/**/*.{js,ts,jsx,tsx}';

@theme {
	--font-sans:
		var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
		'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

@theme {
	--color-background: oklch(1 0 0);
	--color-foreground: oklch(0.145 0 0);
	--color-card: oklch(1 0 0);
	--color-card-foreground: oklch(0.145 0 0);
	--color-popover: oklch(1 0 0);
	--color-popover-foreground: oklch(0.145 0 0);
	--color-primary: oklch(0.205 0 0);
	--color-primary-foreground: oklch(0.985 0 0);
	--color-secondary: oklch(0.97 0 0);
	--color-secondary-foreground: oklch(0.205 0 0);
	--color-muted: oklch(0.97 0 0);
	--color-muted-foreground: oklch(0.556 0 0);
	--color-accent: oklch(0.97 0 0);
	--color-accent-foreground: oklch(0.205 0 0);
	--color-destructive: oklch(0.577 0.245 27.325);
	--color-destructive-foreground: oklch(0.577 0.245 27.325);
	--color-border: oklch(0.922 0 0);
	--color-input: oklch(0.922 0 0);
	--color-ring: oklch(0.708 0 0);
	--radius: 0.625rem;
}

@media (prefers-color-scheme: dark) {
	@theme {
		--color-background: oklch(0.145 0 0);
		--color-foreground: oklch(0.985 0 0);
		--color-card: oklch(0.185 0 0);
		--color-card-foreground: oklch(0.985 0 0);
		--color-popover: oklch(0.145 0 0);
		--color-popover-foreground: oklch(0.985 0 0);
		--color-primary: oklch(0.985 0 0);
		--color-primary-foreground: oklch(0.205 0 0);
		--color-secondary: oklch(0.269 0 0);
		--color-secondary-foreground: oklch(0.985 0 0);
		--color-muted: oklch(0.269 0 0);
		--color-muted-foreground: oklch(0.708 0 0);
		--color-accent: oklch(0.269 0 0);
		--color-accent-foreground: oklch(0.985 0 0);
		--color-destructive: oklch(0.396 0.141 25.723);
		--color-destructive-foreground: oklch(0.637 0.237 25.331);
		--color-border: oklch(0.269 0 0);
		--color-input: oklch(0.269 0 0);
		--color-ring: oklch(0.439 0 0);
	}
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}

	body {
		@apply bg-background text-foreground;
	}
}

:root {
	--sidebar:
		hsl(0 0% 98%);
	--sidebar-foreground:
		hsl(240 5.3% 26.1%);
	--sidebar-primary:
		hsl(240 5.9% 10%);
	--sidebar-primary-foreground:
		hsl(0 0% 98%);
	--sidebar-accent:
		hsl(240 4.8% 95.9%);
	--sidebar-accent-foreground:
		hsl(240 5.9% 10%);
	--sidebar-border:
		hsl(220 13% 91%);
	--sidebar-ring:
		hsl(217.2 91.2% 59.8%);
}

.dark {
	--sidebar:
		hsl(240 5.9% 10%);
	--sidebar-foreground:
		hsl(240 4.8% 95.9%);
	--sidebar-primary:
		hsl(224.3 76.3% 48%);
	--sidebar-primary-foreground:
		hsl(0 0% 100%);
	--sidebar-accent:
		hsl(240 3.7% 15.9%);
	--sidebar-accent-foreground:
		hsl(240 4.8% 95.9%);
	--sidebar-border:
		hsl(240 3.7% 15.9%);
	--sidebar-ring:
		hsl(217.2 91.2% 59.8%);
}

@theme inline {
	--color-sidebar:
		var(--sidebar);
	--color-sidebar-foreground:
		var(--sidebar-foreground);
	--color-sidebar-primary:
		var(--sidebar-primary);
	--color-sidebar-primary-foreground:
		var(--sidebar-primary-foreground);
	--color-sidebar-accent:
		var(--sidebar-accent);
	--color-sidebar-accent-foreground:
		var(--sidebar-accent-foreground);
	--color-sidebar-border:
		var(--sidebar-border);
	--color-sidebar-ring:
		var(--sidebar-ring);
}


'use client'

import { useEffect, useState } from 'react'

import { api } from '@cashanova/backend/api'
import { useAction } from '@cashanova/backend/react'
import { toast } from '@cashanova/ui/toast'
import { tryCatch } from '@cashanova/utils'

export function useLinkToken() {
	const [linkToken, setLinkToken] = useState<string | null>(null)
	const [isLoading, setIsLoading] = useState(false)

	const createLinkToken = useAction(api.plaid.actions.createLinkToken)

	useEffect(() => {
		setIsLoading(true)
		const fetchLinkToken = async () => {
			const linkToken = await createLinkToken()
			setLinkToken(linkToken)

			setIsLoading(false)
		}

		tryCatch(fetchLinkToken()).then(({ error }) => {
			if (error) {
				toast.error(error.message)
				return
			}

			setIsLoading(false)
		})
	}, [])

	return { linkToken, isLoading }
}

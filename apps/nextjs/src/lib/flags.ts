import { auth, clerkClient } from '@clerk/nextjs/server'
import { flag } from 'flags/next'

export const onboardedFlag = flag({
	key: 'onboarded',
	defaultValue: false,
	decide: async () => {
		const authData = await auth()
		if (!authData.userId) return false

		const clerk = await clerkClient()
		const { onboarded } = await clerk.users
			.getUser(authData.userId)
			.then((user) => user.privateMetadata)

		return onboarded ?? false
	},
})

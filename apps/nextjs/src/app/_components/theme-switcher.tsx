'use client'

import { MoonIcon, SunIcon } from 'lucide-react'

import { DropdownMenuItem } from '@cashanova/ui/dropdown-menu'
import { useTheme } from '@cashanova/ui/theme'

export function ThemeSwitcher() {
	const { resolvedTheme, setTheme } = useTheme()

	return (
		<DropdownMenuItem
			onClick={() => setTheme(resolvedTheme === 'dark' ? 'light' : 'dark')}
		>
			<SunIcon className="size-4 scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90" />
			<MoonIcon className="absolute size-4 scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0" />
			<span>Switch Theme</span>
		</DropdownMenuItem>
	)
}

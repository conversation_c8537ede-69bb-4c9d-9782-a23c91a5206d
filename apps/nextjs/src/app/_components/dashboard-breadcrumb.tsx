'use client'

import { Fragment, useMemo } from 'react'
import { usePathname } from 'next/navigation'

import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator,
} from '@cashanova/ui/breadcrumb'

export function DashboardBreadcrumb() {
	const pathname = usePathname()

	const breadcrumbs = useMemo(() => {
		const pathParts = pathname.split('/')
		const breadcrumbs = pathParts.map((part, index) => {
			const baseUrl =
				process.env.NODE_ENV === 'development'
					? 'http://localhost:3000'
					: 'https://cashanova.ai'
			const href = `${baseUrl}${pathParts.slice(0, index + 1).join('/')}`
			return {
				href,
				label: part.charAt(0).toUpperCase() + part.slice(1),
			}
		})

		return breadcrumbs
	}, [pathname])

	return (
		<Breadcrumb>
			<BreadcrumbList>
				{breadcrumbs.map((breadcrumb, index) => (
					<Fragment key={breadcrumb.href}>
						<BreadcrumbItem>
							{index !== breadcrumbs.length - 1 && (
								<BreadcrumbLink href={breadcrumb.href}>
									{breadcrumb.label}
								</BreadcrumbLink>
							)}
							{index === breadcrumbs.length - 1 && (
								<BreadcrumbPage>{breadcrumb.label}</BreadcrumbPage>
							)}
						</BreadcrumbItem>
						{index > 0 && index < breadcrumbs.length - 1 && (
							<BreadcrumbSeparator />
						)}
					</Fragment>
				))}
			</BreadcrumbList>
		</Breadcrumb>
	)
}

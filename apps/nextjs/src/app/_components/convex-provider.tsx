'use client'

import { useAuth } from '@clerk/nextjs'

import { ConvexProviderWithClerk, ConvexReactClient } from '@cashanova/backend/react'

import { env } from '~/env'

export const convex = new ConvexReactClient(env.NEXT_PUBLIC_CONVEX_URL)

export default function ConvexProvider(props: { children: React.ReactNode }) {
	return (
		<ConvexProviderWithClerk client={convex} useAuth={useAuth}>
			{props.children}
		</ConvexProviderWithClerk>
	)
}

'use client'

import type { DropdownMenuContentProps } from '@cashanova/ui/dropdown-menu'

import { DropdownMenuContent } from '@cashanova/ui/dropdown-menu'
import { useIsMobile } from '@cashanova/ui/use-mobile'

interface NavDropdownContentProps extends DropdownMenuContentProps {
	children: React.ReactNode
}

export function NavDropdownContent({ children, ...rest }: NavDropdownContentProps) {
	const isMobile = useIsMobile()

	return (
		<DropdownMenuContent {...rest} side={isMobile ? 'bottom' : 'right'}>
			{children}
		</DropdownMenuContent>
	)
}

export function DevRibbon() {
	if (process.env.NODE_ENV !== 'development') return null

	return (
		<div className="fixed top-0 right-0 z-50">
			<div className="relative w-32 origin-top-right translate-x-6 translate-y-16 rotate-45 transform bg-red-500 text-white shadow-lg">
				<div className="flex items-center justify-center px-4 py-1">
					<span className="text-xs font-bold tracking-wider uppercase">
						Dev
					</span>
				</div>
			</div>
		</div>
	)
}

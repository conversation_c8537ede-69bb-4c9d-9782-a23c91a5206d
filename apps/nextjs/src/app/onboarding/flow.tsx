'use client'

import { Authenticated, Unauthenticated } from '@cashanova/backend/react'
import { Card, CardContent } from '@cashanova/ui/card'
import { Skeleton } from '@cashanova/ui/skeleton'

import { Throbber } from '../_components/throbber'
import { ConfirmIncome } from './_steps/confirm-income'
import { ConnectBank } from './_steps/connect-bank'
import { FetchTransactions } from './_steps/fetch-transactions'
import { Overview } from './_steps/overview'
import { TransactionAnalysis } from './_steps/transaction-analysis'
import { useOnboarding } from './context'

export default function OnboardingFlow() {
	const { state } = useOnboarding()
	if (!state) {
		return (
			<div className="flex h-full items-center justify-center">
				<Throbber />
			</div>
		)
	}

	return (
		<>
			<Authenticated>
				<div className="space-y-6">
					<div className="text-center">
						<h1 className="text-2xl font-bold">
							Just a few steps before we get started
						</h1>
						<p className="text-muted-foreground">
							Let&apos;s set up your financial profile
						</p>
					</div>

					<Card>
						<CardContent>
							{state.currentStep === 1 && <ConnectBank />}
							{state.currentStep === 2 && <FetchTransactions />}
							{state.currentStep === 3 && <ConfirmIncome />}
							{state.currentStep === 4 && <TransactionAnalysis />}
							{state.currentStep === 5 && <Overview />}
						</CardContent>
					</Card>
				</div>
			</Authenticated>
			<Unauthenticated>
				<div className="space-y-6">
					<div className="text-center">
						<Skeleton className="mx-auto mb-2 h-8 w-80" />
						<Skeleton className="mx-auto h-5 w-64" />
					</div>

					<Card>
						<CardContent className="space-y-6">
							<div className="space-y-4">
								<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
									{Array.from({ length: 4 }).map((_, i) => (
										<div
											key={i}
											className="flex items-center space-x-3 rounded-lg border p-4"
										>
											<Skeleton className="h-10 w-10 rounded-full" />
											<div className="flex-1 space-y-2">
												<Skeleton className="h-4 w-32" />
												<Skeleton className="h-3 w-24" />
											</div>
										</div>
									))}
								</div>

								<div className="space-y-3">
									<div className="flex space-x-4">
										<Skeleton className="h-4 w-20" />
										<Skeleton className="h-4 w-32" />
										<Skeleton className="h-4 w-24" />
									</div>
									{Array.from({ length: 3 }).map((_, i) => (
										<div key={i} className="flex space-x-4 py-2">
											<Skeleton className="h-4 w-24" />
											<Skeleton className="h-4 w-20" />
											<Skeleton className="h-4 w-16" />
										</div>
									))}
								</div>
							</div>

							<div className="flex justify-between pt-4">
								<Skeleton className="h-10 w-32" />
								<Skeleton className="h-10 w-24" />
							</div>
						</CardContent>
					</Card>
				</div>
			</Unauthenticated>
		</>
	)
}

'use client'

import { useEffect, useRef } from 'react'

import { api } from '@cashanova/backend/api'
import { useAction } from '@cashanova/backend/react'
import { toast } from '@cashanova/ui/toast'

import { Throbber } from '~/app/_components/throbber'
import { useOnboarding } from '../context'

export function FetchTransactions() {
	useFetchTransactions()

	return (
		<div className="space-y-6">
			<div className="text-center">
				<h2 className="text-xl font-semibold">Fetching Transactions</h2>
				<p className="text-muted-foreground mt-2">
					Fetching your transactions to determine your income sources and
					expenses.
				</p>
			</div>

			<Throbber />
		</div>
	)
}

function useFetchTransactions() {
	const { nextStep } = useOnboarding()
	const fetchInitialTransactions = useAction(
		api.onboarding.actions.fetchInitialTransactions
	)
	const hasFetched = useRef(false)

	useEffect(() => {
		if (hasFetched.current) return

		hasFetched.current = true
		void fetchInitialTransactions()
			.then(() => nextStep())
			.catch((error) => {
				toast.error(error.message)
			})
	}, [fetchInitialTransactions])
}

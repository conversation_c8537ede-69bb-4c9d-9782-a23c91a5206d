'use client'

import Link from 'next/link'

import { api } from '@cashanova/backend/api'
import { useAction, useQuery } from '@cashanova/backend/react'
import { Button } from '@cashanova/ui/button'
import { Card, CardContent } from '@cashanova/ui/card'
import { DataTable } from '@cashanova/ui/data-table'
import { Progress } from '@cashanova/ui/progress'

export function Overview() {
	const budgets = useQuery(api.budgets.query.getBudgets)
	const markAsOnboarded = useAction(api.onboarding.actions.markAsOnboarded)

	return (
		<div className="space-y-6">
			<div className="text-center">
				<h1 className="text-2xl font-bold">Overview</h1>
				<p className="text-muted-foreground">
					We&apos;ve analyzed your financial profile and created a budget for
					you.
				</p>
			</div>

			<Card>
				<CardContent>
					{budgets?.categories && (
						<DataTable
							columns={[
								{
									header: 'Category',
									accessorKey: 'name',
								},
								{
									header: 'Amount Allocated',
									accessorKey: 'amountAllocated',
								},
								{
									header: 'Usage',
									cell: ({ row }) => {
										const category = row.original
										const usage =
											category.amountSpent /
											category.amountAllocated

										return (
											<Progress
												value={usage * 100}
												className="w-full"
											/>
										)
									},
								},
							]}
							data={budgets.categories}
						/>
					)}
					<div className="flex justify-end">
						<Button
							asChild
							onClick={() => {
								markAsOnboarded()
							}}
						>
							<Link href={'/home'}>Go To Dashboard</Link>
						</Button>
					</div>
				</CardContent>
			</Card>
		</div>
	)
}

'use client'

import { useEffect, useState } from 'react'

import { api } from '@cashanova/backend/api'
import { useAction } from '@cashanova/backend/react'
import { Card, CardContent } from '@cashanova/ui/card'
import { Progress } from '@cashanova/ui/progress'
import { toast } from '@cashanova/ui/toast'

import { Throbber } from '~/app/_components/throbber'
import { useOnboarding } from '../context'

export function TransactionAnalysis() {
	const { progress } = useTransactionAnalysis()

	return (
		<div className="space-y-6">
			<div className="text-center">
				<h2 className="text-xl font-semibold">Analyzing Transactions</h2>
				<p className="text-muted-foreground mt-2">
					Analyzing your transactions to determine your income sources and
				</p>
			</div>

			<Card>
				<CardContent>
					<div className="space-y-4">
						<p className="text-muted-foreground text-center text-sm">
							Analyzing your transactions to determine your income sources
							and expenses.
						</p>

						<Throbber />
					</div>
				</CardContent>
			</Card>
		</div>
	)
}

function useTransactionAnalysis() {
	const [progress, setProgress] = useState(0)
	const analyzeTransactions = useAction(api.ai.thread.analyzeTransactions)
	const { nextStep, updateState } = useOnboarding()

	useEffect(() => {
		setProgress(50)
		void analyzeTransactions({
			saveBudget: true,
		})
			.then((result) => {
				setProgress(100)

				updateState({
					budgetCategories: result.categories,
					incomeSources: result.incomeSources,
				})

				setTimeout(() => {
					nextStep()
				}, 2000)
			})
			.catch((error) => {
				toast.error('Failed to analyze transactions')
			})
	}, [analyzeTransactions, nextStep])

	return { progress }
}

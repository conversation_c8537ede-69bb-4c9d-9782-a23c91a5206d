'use client'

import { useEffect } from 'react'

import { api } from '@cashanova/backend/api'
import { useQuery } from '@cashanova/backend/react'
import { Button } from '@cashanova/ui/button'
import { Checkbox } from '@cashanova/ui/checkbox'
import { DataTable } from '@cashanova/ui/data-table'

import { useOnboarding } from '../context'

export function ConfirmIncome() {
	const { state, nextStep, updateState } = useOnboarding()
	const incomeSources = useQuery(api.transactions.queries.getIncomeSources)

	return (
		<div className="space-y-6">
			<div className="text-center">
				<h2 className="text-xl font-semibold">Verify Income Sources</h2>
				<p className="text-muted-foreground mt-2">
					Verify the income sources and transactions
				</p>
			</div>

			{state?.incomeSources.length < 0 && (
				<div className="text-center">Searching for income sources...</div>
			)}

			<DataTable
				columns={[
					{
						header: 'Name',
						accessorKey: 'name',
					},
					{
						header: 'Amount',
						accessorKey: 'amount',
					},
					{
						header: 'Frequency',
						accessorKey: 'frequency',
					},
					{
						header: 'Confirmed',
						cell: ({ row }) => {
							return (
								<Checkbox
									checked={state?.confirmedIncomeSources.some(
										(incomeSource) =>
											incomeSource.name === row.original.name
									)}
									onCheckedChange={(checked) => {
										if (checked) {
											updateState({
												confirmedIncomeSources: [
													...state?.confirmedIncomeSources,
													row.original,
												],
											})
										}
									}}
								/>
							)
						},
					},
				]}
				data={state.incomeSources}
			/>

			<div className="flex w-full justify-end">
				<Button
					disabled={
						state?.incomeSources.length < 0 ||
						state?.confirmedIncomeSources.length < 1
					}
					onClick={() => nextStep()}
				>
					Confirm Income Sources
				</Button>
			</div>
		</div>
	)
}

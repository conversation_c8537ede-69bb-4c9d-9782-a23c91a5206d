'use client'

import { <PERSON><PERSON><PERSON>, CreditCard, Lock, PiggyBank, Shield } from 'lucide-react'
import { usePlaidLink } from 'react-plaid-link'

import { api } from '@cashanova/backend/api'
import { useAction, useQuery } from '@cashanova/backend/react'
import { Button } from '@cashanova/ui/button'
import { Card } from '@cashanova/ui/card'
import { DataTable } from '@cashanova/ui/data-table'
import { toast } from '@cashanova/ui/toast'

import { useLinkToken } from '~/hooks/use-link-token'
import { useOnboarding } from '../context'

export function ConnectBank() {
	const { nextStep, state } = useOnboarding()

	return (
		<div className="space-y-6">
			<div className="text-center">
				<h2 className="text-xl font-semibold">Connect Your Bank Account</h2>
				<p className="text-muted-foreground mt-2">
					Securely connect your bank accounts to get started
				</p>
			</div>

			<AccountsList />

			<div className="rounded-lg p-4">
				<p className="text-muted-foreground text-sm">
					We use Plaid to securely connect to your bank. Your credentials are
					never stored on our servers.
				</p>
			</div>

			<div className="flex justify-between">
				<OnboardingPlaidLinkButton />

				<Button
					variant={'outline'}
					className="cursor-pointer"
					disabled={state?.accounts.length === 0}
					onClick={() => nextStep()}
				>
					Next Step
					<ArrowRight className="size-4" />
				</Button>
			</div>
		</div>
	)
}

function AccountsList() {
	const accounts = useQuery(api.accounts.queries.getAccounts)

	if (!accounts?.length) {
		return (
			<div className="grid grid-cols-1 md:grid-cols-2">
				<Card className="hover:border-primary/20 cursor-pointer border-2 border-transparent p-4 transition-all">
					<div className="flex items-center space-x-3">
						<div className="bg-background/80 rounded-full p-2">
							<Lock className="text-primary h-5 w-5" />
						</div>
						<div>
							<h3 className="font-medium">Secure Connection</h3>
							<p className="text-muted-foreground text-sm">
								Bank-level security for your data
							</p>
						</div>
					</div>
				</Card>

				<Card className="hover:border-primary/20 cursor-pointer border-2 border-transparent p-4 transition-all">
					<div className="flex items-center space-x-3">
						<div className="bg-background/80 rounded-full p-2">
							<PiggyBank className="text-primary h-5 w-5" />
						</div>
						<div>
							<h3 className="font-medium">All Banks Supported</h3>
							<p className="text-muted-foreground text-sm">
								Connect to thousands of banks
							</p>
						</div>
					</div>
				</Card>

				<Card className="hover:border-primary/20 cursor-pointer border-2 border-transparent p-4 transition-all">
					<div className="flex items-center space-x-3">
						<div className="bg-background/80 rounded-full p-2">
							<CreditCard className="text-primary h-5 w-5" />
						</div>
						<div>
							<h3 className="font-medium">View Transactions</h3>
							<p className="text-muted-foreground text-sm">
								Analyze your spending patterns
							</p>
						</div>
					</div>
				</Card>

				<Card className="hover:border-primary/20 cursor-pointer border-2 border-transparent p-4 transition-all">
					<div className="flex items-center space-x-3">
						<div className="bg-background/80 rounded-full p-2">
							<Shield className="text-primary h-5 w-5" />
						</div>
						<div>
							<h3 className="font-medium">Read-Only Access</h3>
							<p className="text-muted-foreground text-sm">
								We cannot move your money
							</p>
						</div>
					</div>
				</Card>
			</div>
		)
	}

	return (
		<DataTable
			data={accounts}
			columns={[
				{
					header: 'Name',
					accessorKey: 'name',
				},
				{
					header: 'Masked Account Number',
					accessorKey: 'mask',
				},
				{
					header: 'Account Type',
					accessorKey: 'subType',
				},
			]}
		/>
	)
}

function OnboardingPlaidLinkButton() {
	const { linkToken, isLoading } = useLinkToken()

	const handleAccountLink = useAction(api.onboarding.actions.handleAccountLink)

	const { open, ready } = usePlaidLink({
		token: linkToken,
		onSuccess: (publicToken) => {
			handleAccountLink({
				publicToken,
			})
				.then(() => {
					toast.success('Bank connected successfully')
				})
				.catch((error) => {
					toast.error(error.message)
				})
		},
		onExit: (err) => {
			toast.error(err?.display_message ?? 'Bank was not connected')
		},
	})

	return (
		<Button
			variant="default"
			className="cursor-pointer"
			disabled={isLoading || linkToken === null || !ready}
			onClick={() => open()}
		>
			Connect Bank
		</Button>
	)
}

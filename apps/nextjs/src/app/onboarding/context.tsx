'use client'

import type { OnboardingContextType, UpdateOnboardingSchema } from '@cashanova/types'
import { createContext, useContext, useEffect } from 'react'
import { useMutation } from 'convex/react'

import { api } from '@cashanova/backend/api'
import { useDelayedQuery } from '@cashanova/backend/hooks'

const OnboardingContext = createContext<OnboardingContextType | null>(null)

export function OnboardingProvider(props: { children: React.ReactNode }) {
	const onboardingData = useDelayedQuery(api.onboarding.query.getOnboarding)
	const startOnboarding = useMutation(api.onboarding.mutations.startOnboarding)
	const updateOnboarding = useMutation(api.onboarding.mutations.updateOnboarding)

	useEffect(() => {
		if (onboardingData) return

		void startOnboarding()
	}, [onboardingData, startOnboarding])

	const nextStep = () => {
		if (!onboardingData) return
		if (onboardingData.currentStep === 5) return

		void updateOnboarding({
			onboardingId: onboardingData._id,
			opts: {
				currentStep: onboardingData.currentStep + 1,
			},
		})
	}

	const prevStep = () => {
		if (!onboardingData) return
		if (onboardingData.currentStep === 0) return

		void updateOnboarding({
			onboardingId: onboardingData._id,
			opts: {
				currentStep: onboardingData.currentStep - 1,
			},
		})
	}

	const goToStep = (step: number) => {
		if (!onboardingData) return
		if (step < 0 || step > 5) return

		void updateOnboarding({
			onboardingId: onboardingData._id,
			opts: {
				currentStep: step,
			},
		})
	}

	const updateState = (opts: UpdateOnboardingSchema) => {
		if (!onboardingData) return

		void updateOnboarding({
			onboardingId: onboardingData._id,
			opts,
		})
	}

	return (
		<OnboardingContext.Provider
			value={{
				onboardingId: onboardingData?._id ?? null,
				state: onboardingData ?? null,
				updateState,
				nextStep,
				prevStep,
				goToStep,
			}}
		>
			{props.children}
		</OnboardingContext.Provider>
	)
}

export function useOnboarding() {
	const context = useContext(OnboardingContext)
	if (!context) {
		throw new Error('useOnboarding must be used within an OnboardingProvider')
	}

	return context
}

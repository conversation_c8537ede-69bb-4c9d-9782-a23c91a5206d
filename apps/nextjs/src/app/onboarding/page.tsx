import { redirect } from 'next/navigation'
import { RedirectToSignIn } from '@clerk/nextjs'
import { auth } from '@clerk/nextjs/server'

import { onboardedFlag } from '~/lib/flags'
import { OnboardingProvider } from './context'
import OnboardingFlow from './flow'

export default async function OnboardingPage() {
	const { userId } = await auth()
	if (!userId) return <RedirectToSignIn />

	const isOnboarded = await onboardedFlag()
	if (isOnboarded) redirect('/home')

	return (
		<main className="container mx-auto flex h-screen w-full max-w-4xl flex-1 items-center justify-center py-10">
			<OnboardingProvider>
				<OnboardingFlow />
			</OnboardingProvider>
		</main>
	)
}

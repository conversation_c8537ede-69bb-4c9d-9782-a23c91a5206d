import '../styles/globals.css'

import { type Metadata } from 'next'
import { <PERSON><PERSON><PERSON> } from 'next/font/google'
import { ClerkProvider } from '@clerk/nextjs'
import { dark } from '@clerk/themes'

import { ThemeProvider } from '@cashanova/ui/theme'
import { Toaster } from '@cashanova/ui/toast'

import ConvexProvider from '~/app/_components/convex-provider'
import { DevRibbon } from '~/app/_components/dev-ribbon'
import { env } from '~/env'

export const metadata: Metadata = {
	title: 'Cashanova',
	description: 'Seduce your savings goals',
	icons: [{ rel: 'icon', url: '/favicon.ico' }],
}

const geist = Geist({
	subsets: ['latin'],
	variable: '--font-geist-sans',
})

export default function RootLayout({
	children,
}: Readonly<{ children: React.ReactNode }>) {
	return (
		<ClerkProvider
			publishableKey={env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
			appearance={{
				baseTheme: dark,
			}}
		>
			<html lang="en" className={`${geist.variable}`} suppressHydrationWarning>
				<body>
					<ConvexProvider>
						<ThemeProvider
							attribute={'class'}
							defaultTheme="system"
							enableSystem
							disableTransitionOnChange
						>
							<DevRibbon />
							{children}
							<Toaster richColors position="bottom-right" />
						</ThemeProvider>
					</ConvexProvider>
				</body>
			</html>
		</ClerkProvider>
	)
}

'use client'

import { useState } from 'react'
import { Bot } from 'lucide-react'

import { api } from '@cashanova/backend/api'
import { useAction } from '@cashanova/backend/react'
import { Button } from '@cashanova/ui/button'
import { toast } from '@cashanova/ui/toast'

export function RegenerateBudgetButton() {
	const [isLoading, setIsLoading] = useState(false)
	const [toastId, setToastId] = useState<string | number | null>(null)

	const analyzeTransactions = useAction(api.ai.thread.analyzeTransactions)

	return (
		<Button
			disabled={isLoading}
			variant={'outline'}
			className="cursor-pointer"
			onClick={() => {
				setIsLoading(true)

				const _toastId = toast.loading('Analyzing transactions...')
				setToastId(_toastId)

				void analyzeTransactions({
					saveBudget: true,
				})
					.then(() => {
						toast.success('Budget regenerated successfully', {
							id: toastId ?? _toastId,
						})
					})
					.catch((error) => {
						toast.error('Failed to regenerate budget', {
							id: toastId ?? _toastId,
						})
					})
			}}
		>
			<Bot className="size-4" /> Regenerate Budget
		</Button>
	)
}

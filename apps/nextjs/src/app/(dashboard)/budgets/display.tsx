'use client'

import { api } from '@cashanova/backend/api'
import { useDelayedQuery } from '@cashanova/backend/hooks'
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@cashanova/ui/card'
import { DataTable } from '@cashanova/ui/data-table'
import { Skeleton } from '@cashanova/ui/skeleton'
import { formatCurrency } from '@cashanova/utils'

import { RegenerateBudgetButton } from './regenerate'

export function BudgetDisplay() {
	const budget = useDelayedQuery(api.budgets.query.getBudget)
	if (!budget) return <Skeleton className="h-20 w-full" />

	return (
		<div className="flex flex-col gap-4">
			<Card>
				<CardHeader className="flex flex-row items-center justify-between">
					<div className="flex flex-col gap-2">
						<CardDescription className="uppercase">
							Total Budget
						</CardDescription>
						<CardTitle>{formatCurrency(budget.budgetAmount)}</CardTitle>
					</div>
					<RegenerateBudgetButton />
				</CardHeader>
			</Card>
			<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
				{Object.entries(budget.categories).map(([category, data]) => {
					return (
						<Card key={category}>
							<CardHeader>
								<CardDescription className="uppercase">
									{category}
								</CardDescription>
								<CardTitle>
									{formatCurrency(data.amountAllocated)}
								</CardTitle>
							</CardHeader>
							<CardContent>
								<DataTable
									columns={[
										{
											header: 'Name',
											accessorKey: 'name',
										},
										{
											header: 'Allocated',
											accessorKey: 'amountAllocated',
											cell: ({ row }) => {
												return (
													<div>
														{formatCurrency(
															row.original.amountAllocated
														)}
													</div>
												)
											},
										},
										{
											header: 'Spent',
											accessorKey: 'amountSpent',
											cell: ({ row }) => {
												return (
													<div>
														{formatCurrency(
															row.original.amountSpent
														)}
													</div>
												)
											},
										},
									]}
									data={data.subCategories}
								/>
							</CardContent>
						</Card>
					)
				})}
			</div>
		</div>
	)
}

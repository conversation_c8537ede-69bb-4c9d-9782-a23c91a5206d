'use client'

import { api } from '@cashanova/backend/api'
import { Authenticated, useConvexAuth, usePaginatedQuery } from '@cashanova/backend/react'
import { Button } from '@cashanova/ui/button'
import { DataTable } from '@cashanova/ui/data-table'
import { Skeleton } from '@cashanova/ui/skeleton'
import { formatCurrency } from '@cashanova/utils'

import { Throbber } from '~/app/_components/throbber'

export function TransactionsTable() {
	const { isLoading } = useConvexAuth()

	if (isLoading) return <Skeleton className="h-96 w-full" />

	return (
		<Authenticated>
			<TransactionsTableInner />
		</Authenticated>
	)
}

function TransactionsTableInner() {
	const { results, loadMore, status, isLoading } = usePaginatedQuery(
		api.transactions.queries.getTransactions,
		{},
		{
			initialNumItems: 20,
		}
	)

	return (
		<div className="flex flex-col gap-4">
			<DataTable
				columns={[
					{
						header: 'Date',
						cell: ({ row }) => {
							const date = new Date(row.original.date)
							return (
								<div className="flex flex-col">
									<span>
										{date.toLocaleDateString('en-US', {
											day: '2-digit',
											month: '2-digit',
										})}
									</span>
								</div>
							)
						},
					},
					{
						header: 'Name',
						accessorKey: 'description',
					},
					{
						header: 'Amount',
						cell: ({ row }) => {
							return (
								<div className="flex flex-col">
									<span>{formatCurrency(row.original.amount)}</span>
								</div>
							)
						},
					},
					{
						header: 'Category',
						accessorKey: 'category.primary',
					},
				]}
				data={results}
			/>
			{isLoading && <Throbber />}
			<div className="flex w-full justify-end">
				<Button
					disabled={status === 'Exhausted' || status.includes('Loading')}
					onClick={() => loadMore(20)}
				>
					Load More
				</Button>
			</div>
		</div>
	)
}

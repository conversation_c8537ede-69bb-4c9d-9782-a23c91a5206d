import type { NavbarItem } from '@cashanova/types'
import { Suspense } from 'react'
import Link from 'next/link'
import { redirect } from 'next/navigation'
import { RedirectToSignIn, SignOutButton } from '@clerk/nextjs'
import { currentUser } from '@clerk/nextjs/server'
import {
	ChevronsUpDown,
	DollarSign,
	Home,
	LogOut,
	NotepadText,
	Origami,
	Search,
	Settings,
	ShieldCheck,
} from 'lucide-react'

import { Avatar, AvatarFallback, AvatarImage } from '@cashanova/ui/avatar'
import {
	DropdownMenu,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@cashanova/ui/dropdown-menu'
import { Separator } from '@cashanova/ui/separator'
import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarHeader,
	SidebarInset,
	SidebarMenu,
	SidebarMenuButton,
	SidebarProvider,
	SidebarTrigger,
} from '@cashanova/ui/sidebar'
import { Skeleton } from '@cashanova/ui/skeleton'

import { onboardedFlag } from '~/lib/flags'
import { DashboardBreadcrumb } from '../_components/dashboard-breadcrumb'
import { NavDropdownContent } from '../_components/nav-dropdown'

export default async function DashboardLayout(props: { children: React.ReactNode }) {
	const user = await currentUser()
	if (!user) return <RedirectToSignIn />

	const onboarded = await onboardedFlag()
	if (!onboarded) return redirect('/onboarding')

	return (
		<SidebarProvider
			style={
				{
					'--sidebar-width': 'calc(var(--spacing) * 72)',
					'--sidebar-height': 'calc(var(--spacing) * 12',
				} as React.CSSProperties
			}
		>
			<AppSidebar />
			<SidebarInset>
				<header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator
						orientation="vertical"
						className="mr-2 data-[orientation=vertical]:h-4"
					/>
					<DashboardBreadcrumb />
				</header>
				<div className="flex flex-1 flex-col">
					<div className="@container/main flex flex-1 flex-col gap-2">
						<div className="flex flex-1 flex-col gap-4 px-4 py-4 md:gap-6 md:py-6 lg:px-6">
							{props.children}
						</div>
					</div>
				</div>
			</SidebarInset>
		</SidebarProvider>
	)
}

function AppSidebar() {
	return (
		<Sidebar variant="inset">
			<SidebarHeader>
				<SidebarMenu>
					<SidebarMenuButton
						asChild
						className="data-[slot=sidebar-menu-button]:!p-1.5"
						size={'lg'}
					>
						<Link href={'/home'}>
							<Origami className="!size-5 text-green-600" />
							<span className="text-base font-semibold">Cashanova</span>
						</Link>
					</SidebarMenuButton>
				</SidebarMenu>
			</SidebarHeader>
			<SidebarContent>
				<NavMain />
			</SidebarContent>
			<SidebarFooter>
				<Suspense fallback={<Skeleton className="h-10 w-full rounded-lg" />}>
					<NavUser />
				</Suspense>
			</SidebarFooter>
		</Sidebar>
	)
}

async function NavUser() {
	const user = await currentUser()

	return (
		<SidebarMenu>
			<SidebarMenuButton asChild>
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<SidebarMenuButton
							size={'lg'}
							className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
						>
							<Avatar className="size-8 rounded-lg">
								<AvatarImage src={user?.imageUrl} />
								<AvatarFallback className="rounded-lg">
									{user?.firstName?.charAt(0)}
									{user?.lastName?.charAt(0)}
								</AvatarFallback>
							</Avatar>
							<div className="grid flex-1 text-left text-sm leading-tight">
								<span className="truncate font-medium">
									{user?.fullName}
								</span>
								<span className="text-muted-foreground truncate text-xs">
									{user?.emailAddresses[0]?.emailAddress}
								</span>
							</div>
							<ChevronsUpDown className="size-4" />
						</SidebarMenuButton>
					</DropdownMenuTrigger>
					<NavDropdownContent
						className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
						align="end"
						sideOffset={4}
					>
						<DropdownMenuLabel>
							<div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
								<Avatar className="size-8 rounded-lg">
									<AvatarImage src={user?.imageUrl} />
									<AvatarFallback className="rounded-lg">
										{user?.firstName?.charAt(0)}
										{user?.lastName?.charAt(0)}
									</AvatarFallback>
								</Avatar>
								<div className="grid flex-1 text-left text-sm leading-tight">
									<span className="truncate font-medium">
										{user?.fullName}
									</span>
									<span className="text-muted-foreground truncate text-xs">
										{user?.emailAddresses[0]?.emailAddress}
									</span>
								</div>
							</div>
						</DropdownMenuLabel>
						<DropdownMenuSeparator />
						<DropdownMenuItem asChild>
							<Link href={'/account'}>
								<Settings className="mr-2 size-4" />
								Account
							</Link>
						</DropdownMenuItem>
						<DropdownMenuSeparator />
						<SignOutButton>
							<DropdownMenuItem>
								<LogOut className="mr-2 size-4" />
								Log Out
							</DropdownMenuItem>
						</SignOutButton>
					</NavDropdownContent>
				</DropdownMenu>
			</SidebarMenuButton>
		</SidebarMenu>
	)
}

const navItems: NavbarItem[] = [
	{
		label: 'Home',
		icon: <Home className="size-4" />,
		href: '/home',
	},
	{
		label: 'Spending',
		icon: <DollarSign className="size-4" />,
		href: '/spending',
	},
	{
		label: 'Budgets',
		icon: <NotepadText className="size-4" />,
		href: '/budgets',
	},
	{
		label: 'Transactions',
		icon: <Search className="size-4" />,
		href: '/transactions',
	},
	{
		label: 'Rules',
		icon: <ShieldCheck className="size-4" />,
		href: '/rules',
	},
]

function NavMain() {
	return (
		<SidebarMenu>
			{navItems.map((item) => (
				<SidebarMenuButton asChild key={item.label}>
					<Link href={item.href}>
						{item.icon}
						<span className="text-sm font-medium">{item.label}</span>
					</Link>
				</SidebarMenuButton>
			))}
		</SidebarMenu>
	)
}

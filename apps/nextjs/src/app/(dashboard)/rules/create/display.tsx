'use client'

import type { Id } from '@cashanova/backend/dataModel'
import { useState } from 'react'

import { api } from '@cashanova/backend/api'
import { useConvexAuth, useMutation, useQuery } from '@cashanova/backend/react'
import { financeCategories } from '@cashanova/types/transactions'
import { Button } from '@cashanova/ui/button'
import { Card, CardDescription, CardHeader, CardTitle } from '@cashanova/ui/card'
import { Checkbox } from '@cashanova/ui/checkbox'
import { Input } from '@cashanova/ui/input'
import { Label } from '@cashanova/ui/label'
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@cashanova/ui/select'
import { toast } from '@cashanova/ui/toast'
import { formatCurrency } from '@cashanova/utils'

import { useRuleCreation } from './context'

export default function DisplayTransactionSearchResult() {
	const { selectedTransactionId } = useRuleCreation()
	const { isLoading } = useConvexAuth()
	if (isLoading) {
		return (
			<div className="flex flex-col gap-4">
				<h2 className="text-lg font-medium">Results</h2>
			</div>
		)
	}

	if (!selectedTransactionId) {
		return <DisplayTransactionSearchResultInner />
	}

	return <ActionSelection />
}

function DisplayTransactionSearchResultInner() {
	const { state, setSelectedTransactionId } = useRuleCreation()
	const results = useQuery(api.transactions.queries.searchTransactions, {
		name: state.match.name,
	})

	return (
		<div className="flex max-h-fit flex-col gap-4 overflow-y-auto">
			<h2 className="text-lg font-medium">Results</h2>
			<div className="flex flex-col gap-4">
				{results?.map((result) => (
					<Card
						key={result._id}
						className="cursor-pointer"
						onClick={() => setSelectedTransactionId(result._id)}
					>
						<CardHeader>
							<CardTitle>{result.description}</CardTitle>
							<CardDescription>
								{formatCurrency(result.amount)}
							</CardDescription>
						</CardHeader>
					</Card>
				))}
			</div>
		</div>
	)
}

function ActionSelection() {
	const [isPending, setIsPending] = useState(false)
	const [changeCategory, setChangeCategory] = useState(false)
	const [renameTransaction, setRenameTransaction] = useState(false)

	const createRule = useMutation(api.transactions.mutation.createTransactionRule)
	const { state, updateState, selectedTransactionId } = useRuleCreation()

	return (
		<div className="flex flex-col gap-4">
			<div className="flex flex-col gap-2">
				<div className="flex items-center gap-2">
					<Checkbox
						checked={changeCategory}
						onCheckedChange={() => setChangeCategory(!changeCategory)}
					/>
					<Label>Change Category</Label>
				</div>
				{changeCategory && (
					<Select
						onValueChange={(value) => {
							updateState({
								action: {
									changeCategory: value,
								},
							})
						}}
						value={state.action.changeCategory}
					>
						<SelectTrigger className="w-full">
							<SelectValue placeholder="Select Category" />
						</SelectTrigger>
						<SelectContent>
							{financeCategories.map((category) => (
								<SelectItem key={category} value={category}>
									{category
										.replaceAll('_', ' ')
										.toLowerCase()
										.split(' ')
										.map(
											(word) =>
												word.charAt(0).toUpperCase() +
												word.slice(1)
										)
										.join(' ')}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				)}
			</div>
			<div className="flex flex-col gap-2">
				<div className="flex items-center gap-2">
					<Checkbox
						checked={renameTransaction}
						onCheckedChange={() => setRenameTransaction(!renameTransaction)}
					/>
					<Label>Rename Transaction</Label>
				</div>
				{renameTransaction && (
					<Input
						placeholder="Rename Transaction"
						value={state.action.renameTransaction}
						onChange={(e) => {
							updateState({
								action: { renameTransaction: e.currentTarget.value },
							})
						}}
					/>
				)}
			</div>
			<div className="flex flex-col gap-2">
				<div className="flex items-center gap-2">
					<Checkbox
						checked={state.action.ignoreTransaction}
						onCheckedChange={() =>
							updateState({
								action: {
									ignoreTransaction: !state.action.ignoreTransaction,
								},
							})
						}
					/>
					<Label>Ignore Transaction</Label>
				</div>
			</div>
			<Button
				disabled={isPending}
				onClick={() => {
					if (!selectedTransactionId) return
					setIsPending(true)

					void createRule({
						transactionId: selectedTransactionId as Id<'transactions'>,
						action: {
							changeCategory: state.action.changeCategory,
							renameTransaction: state.action.renameTransaction,
							ignoreTransaction: state.action.ignoreTransaction,
						},
					})
						.then(() => {
							toast.success('Transaction Rule Created')
						})
						.catch((error) => {
							toast.error('Failed to create transaction rule')
							console.error(error)
						})
				}}
			>
				Create Rule
			</Button>
		</div>
	)
}

import { RuleContextProvider } from './context'
import DisplayTransactionSearchResult from './display'
import { CreateRuleForm } from './form'

export default function CreateRulePage() {
	return (
		<div className="px-4 lg:px-6">
			<RuleContextProvider>
				<div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
					<div className="lg:col-span-1 lg:max-w-full">
						<CreateRuleForm />
					</div>
					<DisplayTransactionSearchResult />
				</div>
			</RuleContextProvider>
		</div>
	)
}

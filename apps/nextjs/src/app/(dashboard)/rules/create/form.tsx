'use client'

import { useState } from 'react'

import { cn } from '@cashanova/ui'
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@cashanova/ui/card'
import { Checkbox } from '@cashanova/ui/checkbox'
import { Input } from '@cashanova/ui/input'
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@cashanova/ui/select'
import { Separator } from '@cashanova/ui/separator'

import { useRuleCreation } from './context'

export function CreateRuleForm() {
	const { state } = useRuleCreation()

	return (
		<Card className="@container/card">
			<CardHeader>
				<CardTitle>Create a new rule</CardTitle>
				<CardDescription>
					Determine how transactions should be classified
				</CardDescription>
			</CardHeader>
			<CardContent>
				<MatchField type="name" />
			</CardContent>
		</Card>
	)
}

function MatchField(props: { type: 'name' | 'amount' | 'account' | 'date' }) {
	const [open, setOpen] = useState(true)

	return (
		<div className="bg-background border-card flex flex-col gap-4 rounded-lg">
			<div className={cn('flex items-center gap-2 px-4 pt-4', !open && 'pb-4')}>
				{/* <Checkbox
					checked={open}
					onCheckedChange={() => setOpen(!open)}
					className="mr-2"
				/> */}
				<h2 className="text-lg font-medium">
					{props.type.charAt(0).toUpperCase() + props.type.slice(1)}
				</h2>
			</div>
			{open && <MatchFieldNameContent />}
		</div>
	)
}

function MatchFieldNameContent() {
	const { state, updateState } = useRuleCreation()

	return (
		<div className="flex flex-col">
			<Separator />
			<div className="flex flex-col gap-4 p-4">
				<Select
					defaultValue="contains"
					onValueChange={(value) =>
						updateState({
							match: {
								name: {
									type: value as 'contains' | 'equals',
									value: state.match.name?.value ?? '',
								},
								...state.match,
							},
						})
					}
				>
					<SelectTrigger className="w-full">
						<SelectValue placeholder="Contains" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="contains">Contains</SelectItem>
						<SelectItem value="equals">Equals</SelectItem>
					</SelectContent>
				</Select>
				<Input
					placeholder="Keyword"
					onChange={(e) =>
						updateState({
							match: {
								name: {
									type: state.match.name?.type ?? 'contains',
									value: e.target.value,
								},
							},
						})
					}
				/>
			</div>
		</div>
	)
}

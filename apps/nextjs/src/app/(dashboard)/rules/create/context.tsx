'use client'

import type { RuleContextType, RuleState } from '@cashanova/types/rules'
import { createContext, useCallback, useContext, useState } from 'react'

const RuleContext = createContext<RuleContextType | null>(null)

export function RuleContextProvider(props: { children: React.ReactNode }) {
	const [selectedTransactionId, setSelectedTransactionId] = useState<string | null>(
		null
	)
	const [state, setState] = useState<RuleState>({
		match: {},
		action: {},
	})

	const updateState = useCallback((state: Partial<RuleState>) => {
		setState((prev) => ({
			...prev,
			...state,
		}))
	}, [])

	return (
		<RuleContext.Provider
			value={{
				state,
				updateState,
				selectedTransactionId,
				setSelectedTransactionId,
			}}
		>
			{props.children}
		</RuleContext.Provider>
	)
}

export function useRuleCreation() {
	const context = useContext(RuleContext)
	if (!context)
		throw new Error('useRuleCreation must be used within a RuleContextProvider')

	return context
}

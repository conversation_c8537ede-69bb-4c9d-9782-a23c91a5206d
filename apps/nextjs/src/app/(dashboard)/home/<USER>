import { Suspense } from 'react'
import Link from 'next/link'

import { api } from '@cashanova/backend/api'
import { fetchQuery } from '@cashanova/backend/nextjs'
import { Button } from '@cashanova/ui/button'
import { Skeleton } from '@cashanova/ui/skeleton'

import { getAuthToken } from '~/lib/auth'
import { Accounts } from './accounts'
import { ChartArea } from './chart'
import { TransactionsTable } from './latest-transactions'
import { SectionCards } from './section-cards'

export default function HomePage() {
	return (
		<div className="flex flex-1 flex-col gap-4">
			<Suspense fallback={<SectionCardsLoading />}>
				<SectionCards />
			</Suspense>
			<div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
				<div className="col-span-1 lg:col-span-2">
					<ChartArea />
				</div>
				<Suspense fallback={<AccountsLoading />}>
					<Accounts />
				</Suspense>
			</div>
			<div className="flex items-center justify-between">
				<h2 className="text-muted-foreground text-xs font-medium">
					Latest Transactions
				</h2>
				<Button variant="outline" size="sm" asChild>
					<Link href={'/transactions'}>View All</Link>
				</Button>
			</div>
			<Suspense fallback={<LatestTransactionsLoading />}>
				<LatestTransactions />
			</Suspense>
		</div>
	)
}

async function LatestTransactions() {
	const token = await getAuthToken()
	const transactions = await fetchQuery(
		api.transactions.queries.getTransactionsWithLimit,
		{
			limit: 10,
		},
		{
			token,
		}
	)

	return <TransactionsTable transactions={transactions} />
}

function LatestTransactionsLoading() {
	return <Skeleton className="h-96 w-full" />
}

function SectionCardsLoading() {
	return (
		<div className="grid grid-cols-1 gap-4 lg:grid-cols-4">
			<Skeleton className="h-[250px] w-full" />
			<Skeleton className="h-[250px] w-full" />
			<Skeleton className="h-[250px] w-full" />
			<Skeleton className="h-[250px] w-full" />
		</div>
	)
}

function AccountsLoading() {
	return <Skeleton className="h-[250px] w-full" />
}

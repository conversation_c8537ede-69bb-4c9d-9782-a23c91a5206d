import { TrendingDown, TrendingUp } from 'lucide-react'

import { api } from '@cashanova/backend/api'
import { fetchQuery } from '@cashanova/backend/nextjs'
import { cn } from '@cashanova/ui'
import { Badge } from '@cashanova/ui/badge'
import {
	Card,
	CardAction,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@cashanova/ui/card'
import { Progress } from '@cashanova/ui/progress'
import { formatCurrency } from '@cashanova/utils'

import { getAuthToken } from '~/lib/auth'

export async function SectionCards() {
	const token = await getAuthToken()
	const currentMonthStats = await fetchQuery(
		api.transactions.queries.getCurrentMonthStats,
		{},
		{
			token,
		}
	)

	return (
		<div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
			<Card className="@container/card">
				<CardHeader>
					<CardDescription>Current Month Spend</CardDescription>
					<CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
						{formatCurrency(currentMonthStats.budget.totalSpent)}
					</CardTitle>
					<CardAction>
						<Badge variant={'outline'}>
							{currentMonthStats.budget.percentChangeDirection === 'up' ? (
								<TrendingUp />
							) : (
								<TrendingDown />
							)}
							{currentMonthStats.budget.percentChange}%
						</Badge>
					</CardAction>
				</CardHeader>
				<CardFooter className="flex-col items-start gap-1.5 text-sm">
					<div className="line-clamp-1 flex gap-2 font-medium">
						{currentMonthStats.budget.percentChangeDirection === 'up'
							? 'Trending up this month'
							: 'Trending down this month'}
						<TrendingUp className="size-4" />
					</div>
					<div className="text-muted-foreground">
						{currentMonthStats.budget.percentChangeDirection === 'up'
							? `You are spending more this month by ${formatCurrency(currentMonthStats.budget.spentAmountChange)}`
							: `You are spending less this month by ${formatCurrency(currentMonthStats.budget.spentAmountChange)}`}
					</div>
				</CardFooter>
			</Card>
			<Card className="@container/card">
				<CardHeader>
					<CardDescription>Left To Spend</CardDescription>
					<CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
						{formatCurrency(currentMonthStats.budget.leftToSpend)}
					</CardTitle>
				</CardHeader>
				<CardFooter className="flex-col items-start gap-1.5 text-sm">
					<div className="line-clamp-1 flex gap-2 font-medium">
						Left to spend this month
					</div>
					<div className="text-muted-foreground">
						{currentMonthStats.budget.leftToSpend > 0
							? 'You have enough money to spend'
							: 'You are over your budget'}
					</div>
				</CardFooter>
			</Card>
			<Card className="@container/card">
				<CardHeader>
					<CardDescription>Next Payday</CardDescription>
					<CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
						{formatCurrency(currentMonthStats.payday.amount)}
					</CardTitle>
				</CardHeader>
				<CardFooter className="flex-col items-start gap-1.5 text-sm">
					<div className="line-clamp-1 flex gap-2 font-medium">
						{currentMonthStats.payday.daysUntil} Days until payday
					</div>
					<div className="text-muted-foreground">
						Oh boy, you are going to be broke
					</div>
				</CardFooter>
			</Card>
			<Card className="@container/card">
				<CardHeader>
					<CardDescription>Cash Flow</CardDescription>
					<Progress
						value={
							(currentMonthStats.cashFlow.actualSpent /
								currentMonthStats.cashFlow.actualIncome) *
							100
						}
						max={100}
					/>
					<div className="flex justify-between">
						<div className="text-muted-foreground">
							{formatCurrency(currentMonthStats.cashFlow.actualSpent)}
						</div>
						<div
							className={cn(
								'text-muted-foreground',
								currentMonthStats.cashFlow.actualSpent >
									currentMonthStats.cashFlow.actualIncome
									? 'text-red-500'
									: 'text-green-500'
							)}
						>
							{formatCurrency(currentMonthStats.cashFlow.actualIncome)}
						</div>
					</div>
				</CardHeader>
				<CardFooter className="flex-col items-start gap-1.5 text-sm">
					<div className="line-clamp-1 flex gap-2 font-medium">
						{currentMonthStats.cashFlow.actualSpent >
						currentMonthStats.cashFlow.actualIncome
							? 'OH NO! You are spending more than you are making'
							: 'Looks good to me'}
					</div>
				</CardFooter>
			</Card>
		</div>
	)
}

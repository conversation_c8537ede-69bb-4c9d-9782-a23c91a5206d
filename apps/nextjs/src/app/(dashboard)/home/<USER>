'use client'

import type { ChartConfig } from '@cashanova/ui/chart'
import { useEffect, useState } from 'react'

import { api } from '@cashanova/backend/api'
import { Authenticated, useConvexAuth, useQuery } from '@cashanova/backend/react'
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@cashanova/ui/card'
import {
	Area,
	AreaChart,
	CartesianGrid,
	ChartContainer,
	ChartTooltip,
	ChartTooltipContent,
	XAxis,
	YAxis,
} from '@cashanova/ui/chart'
import { Skeleton } from '@cashanova/ui/skeleton'
import { useIsMobile } from '@cashanova/ui/use-mobile'

const chartConfig = {
	spend: {
		label: 'Spend',
	},
	currentMonth: {
		label: 'This Month',
		color: 'var(--color-primary)',
	},
	lastMonth: {
		label: 'Last Month',
		color: 'var(--color-secondary)',
	},
} satisfies ChartConfig

export function ChartArea() {
	const { isLoading } = useConvexAuth()

	if (isLoading) return <Skeleton className="h-[250px] w-full" />

	return (
		<Authenticated>
			<ChartAreaInner />
		</Authenticated>
	)
}

function ChartAreaInner() {
	const [timeRange, setTimeRange] = useState('30d')

	const isMobile = useIsMobile()
	const transactions = useQuery(api.transactions.queries.getDummySpendingData)

	useEffect(() => {
		if (!isMobile) return

		setTimeRange('7d')
	}, [isMobile])

	if (!transactions) return <Skeleton className="h-[250px] w-full" />

	return (
		<Card className="@container/card">
			<CardHeader>
				<CardTitle>Total Spend</CardTitle>
				<CardDescription>Total spend over the last {timeRange}</CardDescription>
				{/* TODO: Add a dropdown to select the time range */}
			</CardHeader>
			<CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
				<ChartContainer
					config={chartConfig}
					className="aspect-auto h-[250px] w-full"
				>
					<AreaChart data={transactions}>
						<defs>
							<linearGradient
								id="fillCurrentMonth"
								x1="0"
								y1="0"
								x2="0"
								y2="1"
							>
								<stop
									offset={'5%'}
									stopColor="var(--color-primary)"
									stopOpacity={1.0}
								/>
								<stop
									offset={'95%'}
									stopColor="var(--color-primary)"
									stopOpacity={0.1}
								/>
							</linearGradient>
							<linearGradient
								id="fillLastMonth"
								x1="0"
								y1="0"
								x2="0"
								y2="1"
							>
								<stop
									offset={'5%'}
									stopColor="var(--color-secondary)"
									stopOpacity={1.0}
								/>
								<stop
									offset={'95%'}
									stopColor="var(--color-secondary)"
									stopOpacity={0.1}
								/>
							</linearGradient>
						</defs>
						<CartesianGrid vertical={false} />
						<XAxis
							dataKey={'date'}
							tickLine={false}
							axisLine={false}
							tickMargin={8}
							minTickGap={32}
							tickFormatter={(value) => {
								const date = new Date(value)
								return date.toLocaleDateString('en-US', {
									month: 'short',
									day: 'numeric',
								})
							}}
						/>
						<ChartTooltip
							cursor={false}
							defaultIndex={isMobile ? -1 : 10}
							content={
								<ChartTooltipContent
									labelFormatter={(value) => {
										return new Date(value).toLocaleDateString(
											'en-US',
											{
												month: 'short',
												day: 'numeric',
											}
										)
									}}
									indicator="dot"
								/>
							}
						/>
						<Area
							dataKey="lastMonth"
							fill="url(#fillLastMonth)"
							fillOpacity={1}
							stroke="var(--color-secondary)"
							strokeWidth={2}
						/>

						<Area
							dataKey="currentMonth"
							fill="url(#fillCurrentMonth)"
							fillOpacity={1}
							stroke="var(--color-primary)"
							strokeWidth={2}
						/>
					</AreaChart>
				</ChartContainer>
			</CardContent>
		</Card>
	)
}

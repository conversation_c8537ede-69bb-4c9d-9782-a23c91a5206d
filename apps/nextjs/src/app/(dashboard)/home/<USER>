import { api } from '@cashanova/backend/api'
import { fetchQuery } from '@cashanova/backend/nextjs'
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from '@cashanova/ui/accordion'
import { Card, CardContent, CardHeader, CardTitle } from '@cashanova/ui/card'

import { getAuthToken } from '~/lib/auth'

export async function Accounts() {
	const token = await getAuthToken()
	const sortedAccounts = await fetchQuery(
		api.accounts.queries.getAccountsWithCategories,
		{},
		{
			token,
		}
	)

	return (
		<Card className="@container/card">
			<CardHeader>
				<CardTitle>Accounts</CardTitle>
			</CardHeader>
			<CardContent>
				<Accordion type="single" collapsible>
					{/* Checking Accounts */}
					{sortedAccounts.accounts.checking.length > 0 && (
						<AccordionItem value="checking">
							<AccordionTrigger className="flex items-center justify-between text-sm font-medium">
								Checking ({sortedAccounts.accounts.checking.length})
								<span className="text-muted-foreground ml-auto text-xs">
									${sortedAccounts.totals.checking.toLocaleString()}
								</span>
							</AccordionTrigger>
							<AccordionContent>
								<div className="space-y-2 pt-2">
									{sortedAccounts.accounts.checking.map(
										(account, index) => (
											<div
												key={index}
												className="flex justify-between text-sm"
											>
												<span>{account.name}</span>
												<span className="font-medium">
													${account.amount.toLocaleString()}
												</span>
											</div>
										)
									)}
								</div>
							</AccordionContent>
						</AccordionItem>
					)}

					{/* Savings Accounts */}
					{sortedAccounts.accounts.savings.length > 0 && (
						<AccordionItem value="savings">
							<AccordionTrigger className="flex items-center justify-between text-sm font-medium">
								Savings ({sortedAccounts.accounts.savings.length})
								<span className="text-muted-foreground ml-auto text-xs">
									${sortedAccounts.totals.savings.toLocaleString()}
								</span>
							</AccordionTrigger>
							<AccordionContent>
								<div className="space-y-2 pt-2">
									{sortedAccounts.accounts.savings.map(
										(account, index) => (
											<div
												key={index}
												className="flex justify-between text-sm"
											>
												<span>{account.name}</span>
												<span className="font-medium">
													${account.amount.toLocaleString()}
												</span>
											</div>
										)
									)}
								</div>
							</AccordionContent>
						</AccordionItem>
					)}

					{/* Investment Accounts */}
					{sortedAccounts.accounts.investment.length > 0 && (
						<AccordionItem value="investment">
							<AccordionTrigger className="flex items-center justify-between text-sm font-medium">
								Investment ({sortedAccounts.accounts.investment.length})
								<span className="text-muted-foreground ml-auto text-xs">
									${sortedAccounts.totals.investment.toLocaleString()}
								</span>
							</AccordionTrigger>
							<AccordionContent>
								<div className="space-y-2 pt-2">
									{sortedAccounts.accounts.investment.map(
										(account, index) => (
											<div
												key={index}
												className="flex justify-between text-sm"
											>
												<span>{account.name}</span>
												<span className="font-medium">
													${account.amount.toLocaleString()}
												</span>
											</div>
										)
									)}
								</div>
							</AccordionContent>
						</AccordionItem>
					)}

					{/* Debt Accounts */}
					{sortedAccounts.accounts.debt.length > 0 && (
						<AccordionItem value="debt">
							<AccordionTrigger className="flex items-center justify-between text-sm font-medium">
								Debt ({sortedAccounts.accounts.debt.length})
								<span className="ml-auto text-xs text-red-600">
									${sortedAccounts.totals.debt.toLocaleString()}
								</span>
							</AccordionTrigger>
							<AccordionContent>
								<div className="space-y-2 pt-2">
									{sortedAccounts.accounts.debt.map(
										(account, index) => (
											<div
												key={index}
												className="flex justify-between text-sm"
											>
												<span>{account.name}</span>
												<span className="font-medium text-red-600">
													-$
													{Math.abs(
														account.amount
													).toLocaleString()}
												</span>
											</div>
										)
									)}
								</div>
							</AccordionContent>
						</AccordionItem>
					)}
				</Accordion>
			</CardContent>
		</Card>
	)
}

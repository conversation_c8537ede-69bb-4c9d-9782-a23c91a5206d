'use client'

import type { fetchQuery } from '@cashanova/backend/nextjs'

import { DataTable } from '@cashanova/ui/data-table'
import { formatCurrency } from '@cashanova/utils'

export function TransactionsTable(props: {
	transactions: Awaited<ReturnType<typeof fetchQuery>>['data']
}) {
	const { transactions } = props

	return (
		<div className="flex flex-col gap-4">
			<DataTable
				columns={[
					{
						header: 'Date',
						cell: ({ row }) => {
							const transaction =
								row.original as (typeof transactions)[number]
							return (
								<span>
									{new Date(transaction.date).toLocaleDateString(
										'en-US',
										{
											month: '2-digit',
											day: '2-digit',
										}
									)}
								</span>
							)
						},
					},
					{
						header: 'Name',
						accessorKey: 'description',
					},
					{
						header: 'Category',
						accessorKey: 'category.primary',
					},
					{
						header: 'Amount',
						cell: ({ row }) => {
							const transaction =
								row.original as (typeof transactions)[number]
							return <span>{formatCurrency(transaction.amount)}</span>
						},
					},
				]}
				data={transactions}
			/>
		</div>
	)
}

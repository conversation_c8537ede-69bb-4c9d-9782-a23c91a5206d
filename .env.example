# Since .env is gitignored, you can use .env.example to build a new `.env` file when you clone the repo.
# Keep this file up-to-date when you add new variables to \`.env\`.

# This file will be committed to version control, so make sure not to have any secrets in it.
# If you are cloning this repo, create a copy of this file named `.env` and populate it with your secrets.

CLERK_FRONTEND_API=""
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=""
CLERK_SECRET_KEY=""

NEXT_PUBLIC_CONVEX_URL=""

PLAID_CLIENT_ID=""
PLAID_SECRET=""
PLAID_ENV="dev"

GROQ_API_KEY=""
ANTHROPIC_API_KEY=""
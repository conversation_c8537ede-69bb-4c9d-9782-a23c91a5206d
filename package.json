{"name": "<PERSON><PERSON><PERSON>", "private": true, "engines": {"node": ">=22.14.0", "pnpm": ">=9.6.0"}, "packageManager": "pnpm@10.13.1", "scripts": {"build": "turbo run build", "clean": "git clean -xdf node_modules", "clean:workspaces": "turbo run clean", "auth:generate": "pnpm -F @cashanova/auth generate", "db:push": "turbo -F @cashanova/db push", "db:studio": "turbo -F @cashanova/db studio", "dev": "turbo watch dev --continue", "dev:next": "turbo watch dev -F @cashanova/nextjs...", "convex:dev": "turbo run convex:dev -F @cashanova/backend...", "format": "turbo run format --continue -- --cache --cache-location .cache/.prettiercache", "format:fix": "turbo run format --continue -- --write --cache --cache-location .cache/.prettiercache", "lint": "turbo run lint --continue -- --cache --cache-location .cache/.eslintcache", "lint:fix": "turbo run lint --continue -- --fix --cache --cache-location .cache/.eslintcache", "lint:ws": "pnpm dlx sherif@latest", "postinstall": "pnpm lint:ws", "typecheck": "turbo run typecheck", "ui-add": "turbo run ui-add", "android": "expo run:android", "ios": "expo run:ios"}, "devDependencies": {"@cashanova/prettier-config": "workspace:*", "@turbo/gen": "^2.5.5", "@types/minimatch": "^6.0.0", "minimatch": "^10.0.3", "prettier": "catalog:", "turbo": "^2.5.5", "typescript": "^5.8.3"}, "prettier": "@cashanova/prettier-config"}